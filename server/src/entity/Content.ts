import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    BaseEntity,
    ManyToOne,
    ManyToMany,
    JoinTable,
    CreateDateColumn, OneToOne, JoinColumn, OneToMany
} from 'typeorm';
import {ContentCategory} from "./ContentCategory";
import {Favourite} from "@/entity/Favourite";
import { Like } from './Like';
import {AudioTag} from "@/entity/AudioTag";
import {File} from "@/entity/File";
import { AudioFile } from '@/entity/AudioFile';
import { Audio } from '@/entity/Audio';

enum LanguagesEnum {
    ru = 'ru',
    en = 'en',
    de = 'de',
    ua = 'ua',
    it = 'it'
}

@Entity('content')
export class Content extends BaseEntity {
    @PrimaryGeneratedColumn()
    id!: number;

    @CreateDateColumn()
    created_at: Date;

    @Column({default: true})
    active: boolean;

    @OneToOne(() => File, file => file.content, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    preview: File | null;

    @Column({nullable: true})
    author: string

    @Column({ type: 'varchar'})
    slug!: string;

    @Column({ type: 'varchar' })
    title!: string;

    @Column({ type: 'varchar' })
    seo_title!: string;

    @Column({ type: 'varchar' })
    seo_description!: string;

    @Column({ nullable: true })
    telegram: string;

    @Column({ nullable: true })
    instagram: string;

    @Column({ nullable: true })
    email: string;

    @Column({ nullable: true })
    phone: string;

    @Column({ type: 'text' })
    content!: string;

    @Column({
        type: 'enum',
        enum: LanguagesEnum,
        default: LanguagesEnum.ru
    })
    lang!: string;

    @ManyToOne(() => ContentCategory, (category) => category.contents)
    category!: ContentCategory;

    @Column({ default: 0 })
    views: number;

    @ManyToMany(() => Favourite, (favourite) => favourite.parent_id)
    @JoinTable()
    favourites: Favourite[];

    @ManyToMany(() => Like, (like) => like.parent_id)
    @JoinTable()
    likes: Like[]

    @ManyToMany(() => AudioTag, (tag) => tag.contents)
    @JoinTable()
    tags: AudioTag[];

    @Column({type: 'json', nullable: true})
    buttons: {index: number, name: string, link: string}[];

    @Column({nullable: true})
    youtube: string;

    @ManyToMany(() => AudioFile, { nullable: true })
    @JoinTable()
    audioFiles: AudioFile[];

    @ManyToMany(() => Audio, { nullable: true })
    @JoinTable()
    audio: Audio[];
}