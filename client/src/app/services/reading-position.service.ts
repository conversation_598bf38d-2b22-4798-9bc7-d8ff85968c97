import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

export interface ReadingPosition {
  chapter: number;
  scrollPosition: number;
  fullscreenScrollPosition: number;
  timestamp: number;
  bookCode: string;
  progress: number; // percentage through the book
}

@Injectable({
  providedIn: 'root'
})
export class ReadingPositionService {
  private platformId = inject(PLATFORM_ID);
  private readonly STORAGE_KEY = 'readingPositions';
  private readonly MAX_AGE_DAYS = 30;

  /**
   * Get saved reading position for a book
   */
  getReadingPosition(bookId: number, bookCode: string): ReadingPosition | null {
    if (!isPlatformBrowser(this.platformId)) return null;

    try {
      const positions = this.getAllPositions();
      const position = positions[bookId];
      
      if (position && position.bookCode === bookCode) {
        return position;
      }
    } catch (error) {
      console.warn('Failed to get reading position:', error);
    }
    
    return null;
  }

  /**
   * Save reading position for a book
   */
  saveReadingPosition(
    bookId: number, 
    bookCode: string, 
    chapter: number, 
    scrollPosition: number = 0,
    fullscreenScrollPosition: number = 0,
    totalChapters: number = 1
  ): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      const positions = this.getAllPositions();
      
      // Clean old positions
      this.cleanOldPositions(positions);

      // Calculate progress percentage
      const progress = this.calculateProgress(chapter, totalChapters);

      positions[bookId] = {
        chapter,
        scrollPosition,
        fullscreenScrollPosition,
        timestamp: Date.now(),
        bookCode,
        progress
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(positions));
    } catch (error) {
      console.warn('Failed to save reading position:', error);
    }
  }

  /**
   * Update only scroll position (for performance during scrolling)
   */
  updateScrollPosition(
    bookId: number, 
    scrollPosition: number, 
    isFullscreen: boolean = false
  ): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      const positions = this.getAllPositions();
      const position = positions[bookId];
      
      if (position) {
        if (isFullscreen) {
          position.fullscreenScrollPosition = scrollPosition;
        } else {
          position.scrollPosition = scrollPosition;
        }
        position.timestamp = Date.now();
        
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(positions));
      }
    } catch (error) {
      console.warn('Failed to update scroll position:', error);
    }
  }

  /**
   * Calculate reading progress percentage
   */
  calculateProgress(currentChapter: number, totalChapters: number): number {
    if (totalChapters <= 0) return 0;
    return Math.round(((currentChapter + 1) / totalChapters) * 100);
  }

  /**
   * Get reading progress for display
   */
  getReadingProgressText(bookId: number, bookCode: string): string | null {
    const position = this.getReadingPosition(bookId, bookCode);
    if (!position) return null;

    if (position.chapter === 0) {
      return 'Начать чтение';
    }

    return `Страница ${position.chapter + 1} • ${position.progress}%`;
  }

  /**
   * Check if book has been started
   */
  hasReadingProgress(bookId: number, bookCode: string): boolean {
    const position = this.getReadingPosition(bookId, bookCode);
    return position !== null && position.chapter >= 0;
  }

  /**
   * Convert scroll position between normal and fullscreen contexts
   */
  convertScrollPosition(
    scrollPosition: number, 
    fromFullscreen: boolean, 
    contentHeight: number,
    containerHeight: number
  ): number {
    if (contentHeight <= containerHeight) return 0;

    // Calculate relative position as percentage
    const maxScroll = contentHeight - containerHeight;
    const relativePosition = scrollPosition / maxScroll;

    // Apply to target context
    return relativePosition * maxScroll;
  }

  /**
   * Get all reading positions from localStorage
   */
  private getAllPositions(): Record<number, ReadingPosition> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('Failed to parse reading positions:', error);
      return {};
    }
  }

  /**
   * Clean old reading positions (older than MAX_AGE_DAYS)
   */
  private cleanOldPositions(positions: Record<number, ReadingPosition>): void {
    const cutoffTime = Date.now() - (this.MAX_AGE_DAYS * 24 * 60 * 60 * 1000);
    
    Object.keys(positions).forEach(key => {
      const position = positions[parseInt(key)];
      if (position.timestamp < cutoffTime) {
        delete positions[parseInt(key)];
      }
    });
  }

  /**
   * Remove reading position for a book
   */
  removeReadingPosition(bookId: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      const positions = this.getAllPositions();
      delete positions[bookId];
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(positions));
    } catch (error) {
      console.warn('Failed to remove reading position:', error);
    }
  }
}
