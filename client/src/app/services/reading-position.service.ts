import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

export interface ReadingPosition {
  chapter: number;
  scrollPosition: number;
  fullscreenScrollPosition: number;
  timestamp: number;
  bookCode: string;
  progress: number; // percentage through the book
  lastActiveMode?: 'normal' | 'fullscreen'; // последний активный режим
}

@Injectable({
  providedIn: 'root'
})
export class ReadingPositionService {
  private platformId = inject(PLATFORM_ID);
  private readonly STORAGE_KEY_NORMAL = 'readingPositions_normal';
  private readonly STORAGE_KEY_FULLSCREEN = 'readingPositions_fullscreen';
  private readonly STORAGE_KEY_META = 'readingPositions_meta'; // для метаданных (глава, прогресс)
  private readonly MAX_AGE_DAYS = 30;

  /**
   * Get saved reading position for a book
   */
  getReadingPosition(bookId: number, bookCode: string): ReadingPosition | null {
    if (!isPlatformBrowser(this.platformId)) return null;

    try {
      const metaData = this.getMetaData(bookId, bookCode);
      if (!metaData) return null;

      const normalScrollPosition = this.getNormalScrollPosition(bookId);
      const fullscreenScrollPosition = this.getFullscreenScrollPosition(bookId);

      return {
        ...metaData,
        scrollPosition: normalScrollPosition,
        fullscreenScrollPosition: fullscreenScrollPosition
      };
    } catch (error) {
      console.warn('Failed to get reading position:', error);
    }

    return null;
  }

  /**
   * Save reading position for a book
   */
  saveReadingPosition(
    bookId: number,
    bookCode: string,
    chapter: number,
    scrollPosition: number = 0,
    fullscreenScrollPosition: number = 0,
    totalChapters: number = 1,
    lastActiveMode: 'normal' | 'fullscreen' = 'normal'
  ): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      // Сохраняем метаданные (глава, прогресс, режим)
      this.saveMetaData(bookId, bookCode, chapter, totalChapters, lastActiveMode);

      // Сохраняем позицию скролла в соответствующее хранилище
      if (lastActiveMode === 'fullscreen') {
        this.saveFullscreenScrollPosition(bookId, fullscreenScrollPosition);
      } else {
        this.saveNormalScrollPosition(bookId, scrollPosition);
      }
    } catch (error) {
      console.warn('Failed to save reading position:', error);
    }
  }

  /**
   * Update only scroll position (for performance during scrolling)
   */
  updateScrollPosition(
    bookId: number,
    scrollPosition: number,
    isFullscreen: boolean = false
  ): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      if (isFullscreen) {
        this.saveFullscreenScrollPosition(bookId, scrollPosition);
      } else {
        this.saveNormalScrollPosition(bookId, scrollPosition);
      }

      // Обновляем timestamp в метаданных
      const metaData = this.getStorageData(this.STORAGE_KEY_META);
      if (metaData[bookId]) {
        metaData[bookId].timestamp = Date.now();
        localStorage.setItem(this.STORAGE_KEY_META, JSON.stringify(metaData));
      }
    } catch (error) {
      console.warn('Failed to update scroll position:', error);
    }
  }

  /**
   * Calculate reading progress percentage
   */
  calculateProgress(currentChapter: number, totalChapters: number): number {
    if (totalChapters <= 0) return 0;
    return Math.round(((currentChapter + 1) / totalChapters) * 100);
  }

  /**
   * Get reading progress for display
   */
  getReadingProgressText(bookId: number, bookCode: string): string | null {
    const position = this.getReadingPosition(bookId, bookCode);
    if (!position) return null;

    if (position.chapter === 0) {
      return 'Начать чтение';
    }

    return `Страница ${position.chapter + 1} • ${position.progress}%`;
  }

  /**
   * Check if book has been started
   */
  hasReadingProgress(bookId: number, bookCode: string): boolean {
    const position = this.getReadingPosition(bookId, bookCode);
    return position !== null && position.chapter >= 0;
  }

  /**
   * Convert scroll position between normal and fullscreen contexts
   */
  convertScrollPosition(
    scrollPosition: number, 
    fromFullscreen: boolean, 
    contentHeight: number,
    containerHeight: number
  ): number {
    if (contentHeight <= containerHeight) return 0;

    // Calculate relative position as percentage
    const maxScroll = contentHeight - containerHeight;
    const relativePosition = scrollPosition / maxScroll;

    // Apply to target context
    return relativePosition * maxScroll;
  }

  /**
   * Clean old reading positions (older than MAX_AGE_DAYS)
   */
  private cleanOldPositions(positions: Record<number, any>): void {
    const cutoffTime = Date.now() - (this.MAX_AGE_DAYS * 24 * 60 * 60 * 1000);

    Object.keys(positions).forEach(key => {
      const position = positions[parseInt(key)];
      if (position && position.timestamp && position.timestamp < cutoffTime) {
        delete positions[parseInt(key)];
      }
    });
  }

  /**
   * Remove reading position for a book
   */
  removeReadingPosition(bookId: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    try {
      // Удаляем из всех хранилищ
      const metaData = this.getStorageData(this.STORAGE_KEY_META);
      const normalPositions = this.getStorageData(this.STORAGE_KEY_NORMAL);
      const fullscreenPositions = this.getStorageData(this.STORAGE_KEY_FULLSCREEN);

      delete metaData[bookId];
      delete normalPositions[bookId];
      delete fullscreenPositions[bookId];

      localStorage.setItem(this.STORAGE_KEY_META, JSON.stringify(metaData));
      localStorage.setItem(this.STORAGE_KEY_NORMAL, JSON.stringify(normalPositions));
      localStorage.setItem(this.STORAGE_KEY_FULLSCREEN, JSON.stringify(fullscreenPositions));
    } catch (error) {
      console.warn('Failed to remove reading position:', error);
    }
  }

  // Методы для работы с отдельными хранилищами

  /**
   * Get metadata for a book
   */
  private getMetaData(bookId: number, bookCode: string): Omit<ReadingPosition, 'scrollPosition' | 'fullscreenScrollPosition'> | null {
    const metaData = this.getStorageData(this.STORAGE_KEY_META);
    const data = metaData[bookId];

    if (data && data.bookCode === bookCode) {
      return data;
    }
    return null;
  }

  /**
   * Save metadata for a book
   */
  private saveMetaData(bookId: number, bookCode: string, chapter: number, totalChapters: number, lastActiveMode: 'normal' | 'fullscreen'): void {
    const metaData = this.getStorageData(this.STORAGE_KEY_META);

    // Clean old positions
    this.cleanOldPositions(metaData);

    // Calculate progress percentage
    const progress = this.calculateProgress(chapter, totalChapters);

    metaData[bookId] = {
      chapter,
      timestamp: Date.now(),
      bookCode,
      progress,
      lastActiveMode
    };

    localStorage.setItem(this.STORAGE_KEY_META, JSON.stringify(metaData));
  }

  /**
   * Get normal scroll position
   */
  private getNormalScrollPosition(bookId: number): number {
    const positions = this.getStorageData(this.STORAGE_KEY_NORMAL);
    return positions[bookId] || 0;
  }

  /**
   * Save normal scroll position
   */
  private saveNormalScrollPosition(bookId: number, scrollPosition: number): void {
    const positions = this.getStorageData(this.STORAGE_KEY_NORMAL);
    positions[bookId] = scrollPosition;
    localStorage.setItem(this.STORAGE_KEY_NORMAL, JSON.stringify(positions));
  }

  /**
   * Get fullscreen scroll position
   */
  private getFullscreenScrollPosition(bookId: number): number {
    const positions = this.getStorageData(this.STORAGE_KEY_FULLSCREEN);
    return positions[bookId] || 0;
  }

  /**
   * Save fullscreen scroll position
   */
  private saveFullscreenScrollPosition(bookId: number, scrollPosition: number): void {
    const positions = this.getStorageData(this.STORAGE_KEY_FULLSCREEN);
    positions[bookId] = scrollPosition;
    localStorage.setItem(this.STORAGE_KEY_FULLSCREEN, JSON.stringify(positions));
  }

  /**
   * Get data from localStorage
   */
  private getStorageData(key: string): Record<number, any> {
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn(`Failed to parse data from ${key}:`, error);
      return {};
    }
  }
}
