<div *ngIf="content">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{content.title}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="flex items-center justify-between w-full mar_md">
        <div class="flex items-center icons_w no_hover hide_sm">
          <div class="icon-wrap cal_w mar_o">
            <svg width="24" height="27" viewBox="0 0 24 27" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M7.0767 5.53846C6.57209 5.53846 6.15363 5.12 6.15363 4.61538V0.923077C6.15363 0.418462 6.57209 0 7.0767 0C7.58132 0 7.99978 0.418462 7.99978 0.923077V4.61538C7.99978 5.12 7.58132 5.53846 7.0767 5.53846Z"
                fill="var(--font-color1)" />
              <path
                d="M16.9233 5.53846C16.4187 5.53846 16.0002 5.12 16.0002 4.61538V0.923077C16.0002 0.418462 16.4187 0 16.9233 0C17.4279 0 17.8464 0.418462 17.8464 0.923077V4.61538C17.8464 5.12 17.4279 5.53846 16.9233 5.53846Z"
                fill="var(--font-color1)" />
              <path
                d="M7.69232 16.3078C7.53232 16.3078 7.37232 16.2709 7.22462 16.2093C7.06462 16.1478 6.94155 16.0616 6.81847 15.9509C6.59693 15.717 6.46155 15.4093 6.46155 15.077C6.46155 14.917 6.49847 14.757 6.56001 14.6093C6.62155 14.4616 6.7077 14.3263 6.81847 14.2032C6.94155 14.0924 7.06462 14.0063 7.22462 13.9447C7.6677 13.7601 8.22155 13.8586 8.56616 14.2032C8.7877 14.437 8.92309 14.757 8.92309 15.077C8.92309 15.1509 8.91078 15.237 8.89847 15.3232C8.88616 15.397 8.86155 15.4709 8.82463 15.5447C8.80001 15.6186 8.76309 15.6924 8.71386 15.7663C8.67693 15.8278 8.61539 15.8893 8.56616 15.9509C8.33232 16.1724 8.01232 16.3078 7.69232 16.3078Z"
                fill="var(--font-color1)" />
              <path
                d="M11.9998 16.3076C11.8398 16.3076 11.6798 16.2707 11.5321 16.2091C11.3721 16.1476 11.249 16.0614 11.1259 15.9507C10.9044 15.7168 10.769 15.4091 10.769 15.0768C10.769 14.9168 10.8059 14.7568 10.8674 14.6091C10.929 14.4614 11.0151 14.326 11.1259 14.203C11.249 14.0922 11.3721 14.006 11.5321 13.9445C11.9751 13.7476 12.529 13.8584 12.8736 14.203C13.0951 14.4368 13.2305 14.7568 13.2305 15.0768C13.2305 15.1507 13.2182 15.2368 13.2059 15.323C13.1936 15.3968 13.169 15.4707 13.1321 15.5445C13.1074 15.6184 13.0705 15.6922 13.0213 15.766C12.9844 15.8276 12.9228 15.8891 12.8736 15.9507C12.6398 16.1722 12.3198 16.3076 11.9998 16.3076Z"
                fill="var(--font-color1)" />
              <path
                d="M16.3079 16.3076C16.1479 16.3076 15.9879 16.2707 15.8402 16.2091C15.6802 16.1476 15.5571 16.0614 15.4341 15.9507C15.3848 15.8891 15.3356 15.8276 15.2864 15.766C15.2371 15.6922 15.2002 15.6184 15.1756 15.5445C15.1387 15.4707 15.1141 15.3968 15.1018 15.323C15.0895 15.2368 15.0771 15.1507 15.0771 15.0768C15.0771 14.7568 15.2125 14.4368 15.4341 14.203C15.5571 14.0922 15.6802 14.006 15.8402 13.9445C16.2956 13.7476 16.8371 13.8584 17.1818 14.203C17.4033 14.4368 17.5387 14.7568 17.5387 15.0768C17.5387 15.1507 17.5264 15.2368 17.5141 15.323C17.5018 15.3968 17.4771 15.4707 17.4402 15.5445C17.4156 15.6184 17.3787 15.6922 17.3295 15.766C17.2925 15.8276 17.231 15.8891 17.1818 15.9507C16.9479 16.1722 16.6279 16.3076 16.3079 16.3076Z"
                fill="var(--font-color1)" />
              <path
                d="M7.69232 20.6153C7.53232 20.6153 7.37232 20.5785 7.22462 20.5169C7.07693 20.4554 6.94155 20.3691 6.81847 20.2584C6.59693 20.0245 6.46155 19.7045 6.46155 19.3845C6.46155 19.2245 6.49847 19.0645 6.56001 18.9168C6.62155 18.7568 6.7077 18.6216 6.81847 18.5108C7.27386 18.0554 8.11078 18.0554 8.56616 18.5108C8.7877 18.7446 8.92309 19.0645 8.92309 19.3845C8.92309 19.7045 8.7877 20.0245 8.56616 20.2584C8.33232 20.4799 8.01232 20.6153 7.69232 20.6153Z"
                fill="var(--font-color1)" />
              <path
                d="M11.9998 20.6153C11.6798 20.6153 11.3598 20.4799 11.1259 20.2584C10.9044 20.0245 10.769 19.7045 10.769 19.3845C10.769 19.2245 10.8059 19.0645 10.8674 18.9168C10.929 18.7568 11.0151 18.6216 11.1259 18.5108C11.5813 18.0554 12.4182 18.0554 12.8736 18.5108C12.9844 18.6216 13.0705 18.7568 13.1321 18.9168C13.1936 19.0645 13.2305 19.2245 13.2305 19.3845C13.2305 19.7045 13.0951 20.0245 12.8736 20.2584C12.6398 20.4799 12.3198 20.6153 11.9998 20.6153Z"
                fill="var(--font-color1)" />
              <path
                d="M16.3079 20.6153C15.9879 20.6153 15.6679 20.4799 15.4341 20.2583C15.3233 20.1476 15.2371 20.0122 15.1756 19.8522C15.1141 19.7045 15.0771 19.5445 15.0771 19.3845C15.0771 19.2245 15.1141 19.0645 15.1756 18.9168C15.2371 18.7568 15.3233 18.6214 15.4341 18.5107C15.7171 18.2276 16.1479 18.0922 16.5418 18.1783C16.6279 18.1907 16.7018 18.2153 16.7756 18.2522C16.8495 18.2768 16.9233 18.3137 16.9971 18.363C17.0587 18.3999 17.1202 18.4614 17.1818 18.5107C17.4033 18.7445 17.5387 19.0645 17.5387 19.3845C17.5387 19.7045 17.4033 20.0245 17.1818 20.2583C16.9479 20.4799 16.6279 20.6153 16.3079 20.6153Z"
                fill="var(--font-color1)" />
              <path
                d="M22.4613 10.5721H1.53825C1.03363 10.5721 0.615173 10.1536 0.615173 9.64903C0.615173 9.14441 1.03363 8.72595 1.53825 8.72595H22.4613C22.9659 8.72595 23.3844 9.14441 23.3844 9.64903C23.3844 10.1536 22.9659 10.5721 22.4613 10.5721Z"
                fill="var(--font-color1)" />
              <path
                d="M16.9231 26.4615H7.07692C2.58462 26.4615 0 23.8769 0 19.3846V8.92308C0 4.43078 2.58462 1.84616 7.07692 1.84616H16.9231C21.4154 1.84616 24 4.43078 24 8.92308V19.3846C24 23.8769 21.4154 26.4615 16.9231 26.4615ZM7.07692 3.69231C3.55692 3.69231 1.84615 5.40308 1.84615 8.92308V19.3846C1.84615 22.9046 3.55692 24.6154 7.07692 24.6154H16.9231C20.4431 24.6154 22.1538 22.9046 22.1538 19.3846V8.92308C22.1538 5.40308 20.4431 3.69231 16.9231 3.69231H7.07692Z"
                fill="var(--font-color1)" />
            </svg>
          </div>
          <span class="ml-2 text-color">{{content.created_at | date: 'dd/MM/yyyy'}}</span>
        </div>
        <div class="flex items-center icons_w no_hover">
          <div class="icon-wrap clock_w">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z"
                fill="var(--font-color1)" />
              <path
                d="M11.0627 9.23796C11.0627 8.34234 11.0627 7.44047 11.0627 6.54485C11.0627 5.95612 11.4565 5.5365 12.0002 5.5365C12.544 5.5365 12.9377 5.95612 12.9377 6.55111C12.9377 8.15445 12.944 9.75153 12.9315 11.3549C12.9315 11.5428 12.9815 11.6743 13.1127 11.8058C14.1752 12.858 15.2252 13.9165 16.2815 14.9686C16.5065 15.1941 16.6502 15.4572 16.619 15.7828C16.5815 16.1649 16.3815 16.4405 16.019 16.5783C15.6377 16.7223 15.294 16.6346 15.0065 16.3465C14.3377 15.6889 13.6815 15.025 13.019 14.3611C12.494 13.835 11.9752 13.3089 11.444 12.7891C11.1815 12.5323 11.0565 12.2442 11.0627 11.8747C11.069 10.9854 11.0627 10.1085 11.0627 9.23796Z"
                fill="var(--font-color1)" />
            </svg>
          </div>
          <span class="ml-2 text-color">{{content.content | readingTime}}</span>
        </div>
        <button class="flex items-center icons_w" [ngClass]="{'is-liked': content.liked}"
          (click)="like(content)">
          <div class="icon-wrap like_w">
            <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                fill="var(--font-color1)" />
            </svg>
            <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                fill="var(--text-color)" />
            </svg>
          </div>
          <span class="ml-2 text-color">{{content.likes}}</span>
        </button>
        <div [ngClass]="{'in-favourites': content.inFavourites}" (click)="favorites(content)"
          class="flex items-center cursor-pointer icons_w">
          <div class="icon-wrap star_w">
            <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                fill="var(--font-color1)" />
            </svg>
            <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                fill="var(--text-color)" />
            </svg>
          </div>
          <span class="ml-2 text-color hide_cont_md">избранное</span>
        </div>
        <div class="flex items-center cursor-pointer icons_w" (click)="share(content)">
          <div class="icon-wrap share_w">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                fill="var(--font-color1)" />
            </svg>
          </div>
          <span class="ml-2 text-color hide_cont_md">поделиться</span>
        </div>
        <button (click)="contents = !contents" class="flex items-center cursor-pointer icons_w conts_r relative">
          <div class="icon-wrap content_w">
            <svg width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="4" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect width="3" height="2" rx="1" fill="var(--font-color1)" />
              <rect x="4" y="8" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect y="8" width="3" height="2" rx="1" fill="var(--font-color1)" />
              <rect x="4" y="16" width="18" height="2" rx="1" fill="var(--font-color1)" />
              <rect y="16" width="3" height="2" rx="1" fill="var(--font-color1)" />
            </svg>
          </div>
          <span class="ml-2 text-color hide_cont_md">содержание</span>
          @if (contents) {
          <div class="content-context contents_">
            <p class="conts_title">Содержание</p>
            <div class="conts_list">
              @for(header of headers; track $index) {
              <p class="conts_item" (click)="toHeader(header)">{{header.content}}</p>
              }
            </div>
          </div>
          }
        </button>
      </div>
      <div class="main_art-image">
        @if(content.preview) {
        <img [ngSrc]="environment.serverUrl + '/upload/' + content.preview.name" width="930" height="456" loading="lazy"
          alt="i">
        } @else {
        <img ngSrc="assets/images/meadow.avif" width="930" height="456" loading="lazy" alt="pic">
        }
      </div>

      <!-- <div id="content" [innerHTML]="content.content"></div> -->
      <text-interaction id="content" [contentId]="content.id" [contentHtml]="content.content"
        class="book_text_section"></text-interaction>

      <!--        <div [innerHTML]="getSafeContent(content.content)" class="content-first-chunk"></div>-->
      @if(previewData){
      <app-link-preview style="position: absolute;" [style.left.px]="previewPosition.x"
        [style.top.px]="previewPosition.y" [previewData]="previewData" [previewImg]="previewImg"
        [hrefPreview]="hrefPreview" [hrefRedirect]="hrefRedirect"
        (onClose)="hidePreview()"
        appClickOutside (clickOutside)="hidePreview()">
      </app-link-preview>
      }
      <!-- </div> -->
      @if(sanitizedVideoUrl) {
      <div class="responsive-video-container mt-[30px]">
        <iframe [src]="sanitizedVideoUrl" width="100%" height="100%" frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen>
        </iframe>
      </div>
      }

      <div class="library-audio__list" *ngIf="audio.length">
        <div class="library-audio__list-inner">
            <div class="library-audio flex gap-4 items-center"
                 *ngFor="let audio of audio; let i = index" [ngClass]="{'active-audio': audioFileIndex === i}"
                 (click)="play(audio); audioFileIndex = i">
              <div class="library-play cursor-pointer">
                <ng-container *ngTemplateOutlet="playIcon"></ng-container>
              </div>
              <div class="audio-name">{{audio.title}}</div>
              <div class="audio-time">{{secondsToHMS(audio.duration)}}</div>
            </div>
        </div>
      </div>

      <span *ngIf="content.author" class="text-color_">Автор {{content.author}}</span>
      <span class="text-color_">Прочитано {{content.views}} раз</span>

      <div class="social">
        <div class="content_wrap_" *ngIf="content.telegram" (click)="copyToClipboard(content.telegram, 'Telegram')">
          <div class="content_ _telegram"></div>
          <p class="social_par">{{content.telegram}}</p>
        </div>
        <div class="content_wrap_" *ngIf="content.instagram" (click)="copyToClipboard(content.instagram, 'Instagram')">
          <div class="content_ _instagram"></div>
          <p class="social_par">{{content.instagram}}</p>
        </div>
        <div class="content_wrap_" *ngIf="content.email" (click)="copyToClipboard(content.email, 'Email')">
          <div class="content_ _email"></div>
          <p class="social_par">{{content.email}}</p>
        </div>
        <div class="content_wrap_" *ngIf="content.phone" (click)="copyToClipboard(content.phone, 'Телефон')">
          <div class="content_ _phone"></div>
          <p class="social_par">{{content.phone}}</p>
        </div>
      </div>

      <div class="tags_cont" *ngIf="content?.tags">
        @for(tag of content.tags; track tag.id) {
        <a (click)="goToCategory(tag.id)">
          <div class="tag_item">{{tag.name}}</div>
        </a>
        }
      </div>
      @if (content.buttons.length) {
      <div class="buttons">
        @for(button of content.buttons; track $index) {
        <a class="button-content" [href]="button.link">
          <div class="button_cont-wrap">{{button.name}}</div>
        </a>
        }
      </div>
      }

      <div class="lt_wrap">
        <div class="lot_div"></div>
      </div>

      @if(similar.length) {
      <div class="similar-content-section">
        <h3 class="similar-content-title">Похожие статьи:</h3>
        <div class="arrows_cont">
          <p (click)="moveLeft()" class="arr_w"></p>
          <p (click)="moveRight()" class="arr_w trsf"></p>
        </div>
        <div id="carousel" class="similar-items relative">
          @for(item of similar; track $index) {
          <div class="similar-item-card" (click)="handleSimilarItemClick($event, item)"
               role="button"
               tabindex="0"
               (keydown)="handleSimilarItemKeydown($event, item)"
               style="cursor: pointer; position: relative;">
            <!-- Overlay to ensure clicks are captured -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1;"></div>
            <div class="similar-item-image">
              @if(item?.preview) {
              <img [ngSrc]="environment.serverUrl + '/upload/' + item.preview.name" width="930" height="456"
                loading="lazy" alt="i" (error)="handleImageError($event)">
              } @else {
              <img ngSrc="assets/images/meadow.avif" width="930" height="456" loading="lazy" alt="pic">
              }
            </div>
            <div class="similar-item-content">
              <h4 class="similar-item-title">{{item.title}}</h4>
            </div>
          </div>
          }
          <div class="scroll_top" [ngClass]="{'is-visible': showScrollTop}" (click)="scrollToTop()"></div>
        </div>
      </div>
      }
    </div>
  </div>

  <ng-template #playIcon>
    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.6541 6.36495C12.1153 6.6358 12.1153 7.3642 11.6541 7.63505L0.960271 13.9145C0.525241 14.1699 3.49471e-07 13.8225 3.73213e-07 13.2794L9.22176e-07 0.720594C9.45918e-07 0.177449 0.525242 -0.169903 0.960272 0.0855478L11.6541 6.36495Z" fill="#532E00"/>
    </svg>
  </ng-template>
</div>

