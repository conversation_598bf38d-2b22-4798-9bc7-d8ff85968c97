import {Component, inject, PLATFORM_ID, signal, DestroyRef, effect, ViewChild, ElementRef, HostListener, OnD<PERSON>roy} from '@angular/core';
import {CommonModule, isPlatformBrowser, NgOptimizedImage} from "@angular/common";
import {LibraryService} from "@/services/library.service";
import {ReadingPositionService} from "@/services/reading-position.service";
import {DomSanitizer, Meta, Title} from "@angular/platform-browser";
import {ActivatedRoute, Router} from "@angular/router";
import {environment} from "@/env/environment";
import {ProfileService} from "@/services/profile.service";
import {ShareDataService} from "@/services/share-data.service";
import { ToasterService } from '@/services/toaster.service';
import {AdvertisingService} from "@/services/advertising.service";
import {TranslocoModule, TranslocoService} from "@jsverse/transloco";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthService } from '@/services/auth.service';
import {FormsModule} from "@angular/forms";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { TextInteractionComponent } from '../content/text-interaction/text-interaction.component';
import { Track } from '@/interfaces/track';
import { ClickOutsideDirective } from '@/directives/clickOutside';
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component';

@Component({
  selector: 'app-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    TextInteractionComponent,
    TranslocoModule,
    ClickOutsideDirective,
    LoadingIndicatorComponent
  ],
  templateUrl: './library.component.html',
  styleUrl: './library.component.scss'
})
export class LibraryComponent implements OnDestroy {
  shareDataService = inject(ShareDataService)
  profileService = inject(ProfileService);
  libraryService = inject(LibraryService);
  readingPositionService = inject(ReadingPositionService);
  authService = inject(AuthService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  platformId = inject(PLATFORM_ID)
  titleService = inject(Title)
  metaService = inject(Meta);
  advertisingService = inject(AdvertisingService);
  translocoService = inject(TranslocoService);
  auth = inject(AuthService)
  sanitizer = inject(DomSanitizer)
  code = this.route.snapshot.paramMap.get('code');
  selection: any = signal(null)
  toasterService = inject(ToasterService);
  quote: string | null = null
  id: number | null = null
  rendition: any = null
  data: any = null
  currentPage: any
  tabs = ['О книге', 'Читать', 'Аудиокнига']
  tab = 'О книге'
  descriptionTab = 'annotation'
  bookLoaded = false;
  bookId = ""
  private readonly destroyRef = inject(DestroyRef);
  chapter = 0
  theme = 'light'
  fontSize = '17'
  fontFamily = 'Arial'
  audioBookIndex = -1;
  chapterContent: any = ''
  chapterCount: number = 0
  chapterTitles: any = []
  chapterReadingTime: number = 0

  likesContent: any = [];
  favouriteContent: any = [];
  likesCount: number = 0;
  lang = 'ru';
  relatedBooksShowLast: boolean = false;
  relatedBooks = [
    {
      name: 'Панчадаши',
      author: 'Шри Видьяранья Свами',
      bookImgSrc: 'assets/images/book_1.webp'
    },
    {
      name: 'Аватары Господа Шивы',
      author: 'Всемирная Община Санатана Дхармы',
      bookImgSrc: 'assets/images/book_2.webp'
    },
    {
      name: 'Сборник лекций по Шайва-сиддханте',
      author: 'Шри Гуру Свами Вишнудевананда Гири',
      bookImgSrc: 'assets/images/book_3.webp'
    },
  ];
  fullscreenBookTheme = 'light';
  fullscreenBookFont = 'Prata';
  fullscreenBookFontSize: number = 24; // Will be set to responsive default in constructor
  fullscreenBookTextWidth = 'narrow'; // Default to narrow column (700px)
  isFullscreenBookDialogActive: boolean = false;
  showFullscreenOptions: boolean = false;
  similar: any = []
  contents: boolean = false;
  fullscreenContents: boolean = false;
  durationMinutes: any = ''

  // Reading position synchronization properties
  private fullscreenScrollSaveTimeout: any = null;
  private lastSyncedScrollPosition: number = 0;
  // private touchStartX: number = 0;
  // private touchEndX: number = 0;
  // private mouseStartX: number = 0;
  // private mouseDown: boolean = false;
  // private readonly swipeThreshold: number = 30;

  quoteId = this.route.snapshot.queryParams['quoteId']
  libraryQuote: any = null

  constructor() {
    // Set responsive default font size
    this.fullscreenBookFontSize = this.getResponsiveDefaultFontSize();

    // Add window resize listener to update font size responsively
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('resize', () => {
        // Only update to responsive default if user hasn't manually changed font size
        const currentDefault = this.getResponsiveDefaultFontSize();
        if (this.fullscreenBookFontSize === 24 || this.fullscreenBookFontSize === 18) {
          this.fullscreenBookFontSize = currentDefault;
          this.applyFullscreenFontSize();
        }
      });
    }

    effect(() => {
      if(this.profileService.name() && this.profileService.profile) {
        this.setCount();
      }
    });
  }

  // Get responsive default font size based on screen width
  getResponsiveDefaultFontSize(): number {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth <= 768) {
        return 18; // Smaller font for tablet and mobile
      }
    }
    return 24; // Default large font for desktop
  }

  // Calculate proportional line-height based on font size
  calculateProportionalLineHeight(fontSize: number): number {
    return Math.round(fontSize * 1.5); // 1.5 ratio for optimal readability
  }

  ngOnInit() {
    if(this.authService.isAuth){
      this.libraryService.getLikes(this.route.snapshot.params['code']).subscribe((res: any) => {
        this.likesContent = res[0];
        this.likesCount = res[1];
      })
      this.libraryService.getFavourites().subscribe((res: any) => {
        // Проверяем, что res это массив или объект с массивом
        if (Array.isArray(res)) {
          this.favouriteContent = res.map((e: any) => e.id);
        } else if (res && Array.isArray(res.items)) {
          this.favouriteContent = res.items.map((e: any) => e.id);
        } else {
          this.favouriteContent = [];
        }
      })
    }

    // if(this.profileService.name() && this.profileService.profile) {
    //   this.setCount();
    // } else {
    //   this.profileService.setProfile();
    // }

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((lang: any) => {
      if(this.lang === lang) {
        return;
      } else {
        this.lang = lang;
      }
      this.getDate();
    })

    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      const newBookId = params['code'];
      if (newBookId !== this.bookId) {
        // Сбрасываем данные при смене книги
        this.similar = [];
        this.similarCall = false;
        this.tab = 'О книге'; // Возвращаемся на первый таб
        this.chapter = 0; // Сбрасываем главу

        if (isPlatformBrowser(this.platformId)) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
      this.bookId = newBookId;
      this.getDate();
    })

    // Handle query parameters for tab selection
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(queryParams => {
      const tabParam = queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.tab = tabParam;

        // If switching to reading tab, load the first chapter
        if (tabParam === 'Читать') {
          if (this.quoteId) {
            this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
              this.libraryQuote = res;
              this.chapter = res.page;
              this.getChapter(res.page);
            })
          } else {
            // Проверяем сохраненную позицию чтения
            this.restoreReadingPosition();
          }
        }
      }
    })
  }

  ngOnDestroy() {
    // Очищаем timeout при уничтожении компонента
    if (this.scrollSaveTimeout) {
      clearTimeout(this.scrollSaveTimeout);
    }
  }

  get duration() {
    if(!this.data || !this.data.duration) return 0
    const [hours, minutes, seconds] = this.data.duration.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + Math.round(seconds / 60);
    const formattedHours = Math.floor(totalMinutes / 60);
    const formattedMinutes = totalMinutes % 60;
    return `${formattedHours} ч. ${formattedMinutes} мин.`;
  }

  setCount() {
    this.likesContent = this.profileService.profile!.libraryLikes;
    this.favouriteContent = this.profileService.profile!.libraryFavourites;
  }

  getDate(views = true) {
    if(this.bookId)
    this.libraryService.get(this.bookId, views).subscribe((res: any) => {
      this.id = res.id
      this.titleService.setTitle(res.seo_title);
      this.metaService.updateTag({name: 'description', content: res.seo_description})
      this.data = res;

      // Безопасный расчет времени аудио
      if (this.data.audio && Array.isArray(this.data.audio) && this.data.audio.length > 0) {
        const totalSeconds = this.data.audio.reduce((a: number, b: any) => {
          const duration = Number(b.duration);
          return a + (isNaN(duration) ? 0 : duration);
        }, 0);
        this.durationMinutes = Math.ceil(totalSeconds / 60);
      } else {
        this.durationMinutes = 0;
      }

      // Обновляем массив табов в зависимости от наличия аудио
      this.updateTabs();

      // Handle tab selection from query parameters after tabs are updated
      const tabParam = this.route.snapshot.queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.changeTab(tabParam);
      } else if(this.quoteId) {
        this.changeTab('Читать');
      }

      // this.data.audio.forEach((el: any) => {
      //   this.getAudioDuration(el.url).then(duration => {
      //     el.time = this.formatDuration(duration)
      //   })
      //   .catch(error => console.error(error));
      // });
    })
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return [
      hours > 0 ? String(hours).padStart(2, "0") : "00",
      String(minutes).padStart(2, "0"),
      String(secs).padStart(2, "0")
    ].join(":");
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/library/${content.code}`).then(() =>
        this.toasterService.showToast('Ссылка на книгу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }


  favorite(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(id).subscribe({
      next: (r) => {
        if(this.favouriteContent.includes(id)) {
          this.favouriteContent.splice(this.favouriteContent.indexOf(id), 1)
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.favouriteContent.push(id)
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  like(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(id).subscribe({
      next: (r) => {

        if(this.likesContent.includes(id)) {
          this.likesContent.splice(this.likesContent.indexOf(id), 1)
          this.likesCount--;
          this.toasterService.showToast('Книга удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        } else {
          this.likesContent.push(id)
          this.likesCount++;
          this.toasterService.showToast('Книга добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        }

      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  get likes () {
    return this.likesContent.filter((item: any) => item.id === this.data.id);
  }

  changeFontSize(e: Event) {
    const target = e.target as HTMLInputElement;
    if (this.rendition) {
      // Create a style rule that targets all text elements
      const fontRule = `* { font-size: ${target.value}px !important; }`;

      // Register and apply new theme with our font size
      this.rendition.themes.register('custom-size', {
        body: {
          'font-size': `${target.value}px !important`,
        },
        'p, div, span, h1, h2, h3, h4, h5, h6': {
          'font-size': `${target.value}px !important`,
        }
      });

      // Add direct stylesheet injection
      const doc = this.rendition.getContents()[0].document;
      let style = doc.createElement('style');
      style.innerHTML = fontRule;
      doc.head.appendChild(style);

      // Select our custom theme
      this.rendition.themes.select('custom-size');
    }
  }

  changeTheme(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.select(target.value)
    this.rendition.display((document.querySelectorAll('.library-content select option')[1] as HTMLOptionElement).getAttribute('ref'))
    this.rendition.display((document.querySelectorAll('.library-content select option')[0] as HTMLOptionElement).getAttribute('ref'))
  }

  changeFont(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.override("font-family", target.value);
    //this.rendition.themes.font(`${target.value}!important`)
  }

  showQuoteContextMenu(e: MouseEvent) {
    // const selection = window.parent.document.querySelector('iframe')!.contentWindow!.getSelection
    // const offsetTop = (window.parent.document.getElementById('epub')!.offsetParent as HTMLElement).offsetTop + 93 + e.pageY;
    // (document.querySelector('.library-context') as HTMLDivElement).style.left = `${e.screenX}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.top = `${offsetTop}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.display = `block`
    // if(selection()) {
    //   this.selection.set(selection()!.toString())
    //   this.quote = selection()!.toString()
    // }
    return false
  }

  addQuoteToFavourites() {
    if(!this.id || !this.quote) return
    this.hideContextMenu()
    this.libraryService.addQuoteToFavourites(this.id, this.quote, this.currentPage).subscribe((res: any) => {
      this.quote = null
      navigator.clipboard.writeText(environment.baseUrl + '/ru/library/' + this.code + '?quoteId=' + res).then(() => {
        this.hideContextMenu()
      })
    })
  }

  hideContextMenu() {
    (document.querySelector('.library-context') as HTMLDivElement).style.display = `none`
  }

  copySelection(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      navigator.clipboard.writeText(this.selection()).then(() => {
        alert('Copied to clipboard')
        this.hideContextMenu()
      })
    }
  }

  shareQuote(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      this.addQuoteToFavourites()
    }
  }

  getTags() {
    return this.data.tags.map((e: any) => e.name).join(', ');
  }

  changeTab(tab: string) {
    this.tab = tab;

    if(tab == 'Читать') {
      if(this.quoteId) {
        this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
          this.libraryQuote = res;
          this.chapter = res.page;
          this.getChapter(res.page);
        })
      } else {
        // Проверяем сохраненную позицию чтения
        const savedPosition = this.getSavedReadingPosition();
        const startChapter = savedPosition !== null ? savedPosition.chapter : 0;
        this.getChapter(startChapter, savedPosition?.scrollPosition);
      }
    }
  }

  getChapter(index: number, scrollPosition?: number) {
    this.chapter = index; // Обновляем текущую главу
    this.libraryService.getChapter(this.data.id, index).subscribe((res: any) => {
      this.chapterContent = res.content.content;
      this.chapterCount = res.count;
      this.chapterTitles = res.titles;

      // Рассчитываем время чтения для текущей главы
      this.chapterReadingTime = this.calculateReadingTime(this.chapterContent);

      // Сохраняем позицию чтения
      this.saveReadingPosition(index);

      if(this.libraryQuote && this.libraryQuote.page == index) {
        this.chapterContent = this.chapterContent.replace(this.libraryQuote.quote.trim(), `<span class="quote" style="background: var(--selection); color: black;">${this.libraryQuote.quote.trim()}</span>`)

        setTimeout(() => {
          const element: HTMLElement = document.querySelector('.quote')!;
          if(element) {
            window.scrollTo({
              top: element.offsetTop + 100,
              behavior: 'smooth'
            });
          }
        }, 500)

      } else if (scrollPosition !== undefined && scrollPosition > 0) {
        // Восстанавливаем позицию скролла если она была сохранена
        setTimeout(() => {
          if (isPlatformBrowser(this.platformId)) {
            if (this.isFullscreenBookDialogActive) {
              // Restore fullscreen scroll position
              const savedPosition = this.getSavedReadingPosition();
              if (savedPosition && savedPosition.fullscreenScrollPosition > 0) {
                this.setFullscreenScrollPosition(savedPosition.fullscreenScrollPosition);
              }
            } else {
              // Restore normal scroll position
              window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
              });
            }
          }
        }, 500);
      }

    })
  }

  // Метод для расчета времени чтения
  calculateReadingTime(htmlContent: string): number {
    if (!htmlContent) return 0;

    // Удаляем HTML теги и получаем чистый текст
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Считаем слова
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;

    // Средняя скорость чтения 225 слов в минуту
    const wordsPerMinute = 225;
    return Math.ceil(words / wordsPerMinute);
  }

  // Сохранение позиции чтения
  private saveReadingPosition(chapter: number): void {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      const scrollPosition = this.isFullscreenBookDialogActive ? 0 : window.scrollY;
      const fullscreenScrollPosition = this.isFullscreenBookDialogActive ? this.getFullscreenScrollPosition() : 0;
      const lastActiveMode = this.isFullscreenBookDialogActive ? 'fullscreen' : 'normal';

      this.readingPositionService.saveReadingPosition(
        this.data.id,
        this.bookId,
        chapter,
        scrollPosition,
        fullscreenScrollPosition,
        this.chapterCount,
        lastActiveMode
      );
    }
  }

  // Получение сохраненной позиции чтения
  private getSavedReadingPosition(): { chapter: number; scrollPosition: number; fullscreenScrollPosition: number; lastActiveMode?: 'normal' | 'fullscreen' } | null {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      const savedPosition = this.readingPositionService.getReadingPosition(this.data.id, this.bookId);

      console.log('Getting saved reading position:', {
        bookId: this.data.id,
        bookCode: this.bookId,
        savedPosition
      });

      if (savedPosition) {
        const result = {
          chapter: savedPosition.chapter,
          scrollPosition: savedPosition.scrollPosition || 0,
          fullscreenScrollPosition: savedPosition.fullscreenScrollPosition || 0,
          lastActiveMode: savedPosition.lastActiveMode
        };

        console.log('Returning reading position:', result);
        return result;
      }
    }

    console.log('No saved reading position found');
    return null;
  }

  // Восстановление позиции чтения после перезагрузки страницы
  private restoreReadingPosition(): void {
    if (!isPlatformBrowser(this.platformId) || !this.data?.id) return;

    const savedPosition = this.getSavedReadingPosition();
    const startChapter = savedPosition !== null ? savedPosition.chapter : 0;

    // Показываем тостер о восстановлении позиции
    if (savedPosition && savedPosition.chapter > 0) {
      this.toasterService.showToast(
        `Возвращено к последней позиции чтения (страница ${savedPosition.chapter + 1})`,
        'success',
        'bottom-middle',
        3000
      );
    }

    // Загружаем главу с восстановлением позиции
    this.getChapter(startChapter, savedPosition?.scrollPosition);
  }

  // Очистка старых позиций чтения (старше 30 дней)
  private cleanOldReadingPositions(readingPositions: any): void {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    Object.keys(readingPositions).forEach(bookId => {
      const position = readingPositions[bookId];
      if (position.timestamp && position.timestamp < thirtyDaysAgo) {
        delete readingPositions[bookId];
      }
    });
  }

  // Получение текущей позиции скролла в полноэкранном режиме
  private getFullscreenScrollPosition(): number {
    if (!isPlatformBrowser(this.platformId) || !this.fullscreenDialog) return 0;

    const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
    return dialogContent ? dialogContent.scrollTop : 0;
  }

  // Установка позиции скролла в полноэкранном режиме
  private setFullscreenScrollPosition(scrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId) || !this.fullscreenDialog) {
      console.log('Cannot set fullscreen scroll position: no dialog');
      return;
    }

    const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
    if (dialogContent) {
      const maxScroll = dialogContent.scrollHeight - dialogContent.clientHeight;

      console.log('Setting fullscreen scroll position:', {
        scrollPosition,
        maxScroll,
        scrollHeight: dialogContent.scrollHeight,
        clientHeight: dialogContent.clientHeight
      });

      if (maxScroll <= 0) {
        console.log('Content is too short for scrolling, maxScroll:', maxScroll);
        return;
      }

      // Ограничиваем позицию максимально возможным значением
      const clampedPosition = Math.min(scrollPosition, maxScroll);
      dialogContent.scrollTop = clampedPosition;

      // Проверяем, что позиция установилась
      setTimeout(() => {
        console.log('Scroll position after setting:', {
          requested: scrollPosition,
          clamped: clampedPosition,
          actual: dialogContent.scrollTop
        });
      }, 50);
    } else {
      console.log('Cannot set fullscreen scroll position: no container');
    }
  }

  // Синхронизация позиций скролла между обычным и полноэкранным режимами
  private syncScrollPositions(fromFullscreen: boolean = false): void {
    if (!isPlatformBrowser(this.platformId) || !this.data?.id) return;

    if (fromFullscreen) {
      // Синхронизация из полноэкранного режима в обычный
      const fullscreenScrollPosition = this.getFullscreenScrollPosition();
      this.readingPositionService.updateScrollPosition(this.data.id, fullscreenScrollPosition, true);
    } else {
      // Синхронизация из обычного режима в полноэкранный
      const normalScrollPosition = window.scrollY;
      this.readingPositionService.updateScrollPosition(this.data.id, normalScrollPosition, false);
    }
  }

  // Прокрутка основного вида к соответствующей позиции из полноэкранного режима
  private scrollToCorrespondingPosition(fullscreenScrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    console.log('Attempting to scroll to corresponding position:', fullscreenScrollPosition);

    // Ждем, пока контент загрузится в основном виде
    const waitForContent = (attempts: number = 0) => {
      if (attempts > 20) { // Максимум 2 секунды ожидания
        console.log('Timeout waiting for content, using fallback');
        this.fallbackScrollSync(fullscreenScrollPosition);
        return;
      }

      const mainTextContainer = document.querySelector('.book_text_section');
      if (!mainTextContainer || !this.chapterContent) {
        // Если контент еще не загружен, ждем еще немного
        setTimeout(() => waitForContent(attempts + 1), 100);
        return;
      }

      console.log('Content loaded, attempting text-based sync');

      // Сначала пробуем простую пропорциональную синхронизацию
      this.fallbackScrollSync(fullscreenScrollPosition);

      // Затем пробуем улучшенную синхронизацию через текстовые элементы
      setTimeout(() => {
        this.attemptTextBasedSync(fullscreenScrollPosition);
      }, 500);
    };

    waitForContent();
  }

  // Попытка синхронизации на основе текстовых элементов
  private attemptTextBasedSync(fullscreenScrollPosition: number): void {
    const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
    const mainTextContainer = document.querySelector('.book_text_section');

    if (!fullscreenContainer || !mainTextContainer) return;

    // Вычисляем относительную позицию в полноэкранном режиме
    const fullscreenContentHeight = fullscreenContainer.scrollHeight;
    const fullscreenViewHeight = fullscreenContainer.clientHeight;
    const maxFullscreenScroll = fullscreenContentHeight - fullscreenViewHeight;

    if (maxFullscreenScroll <= 0) return;

    const relativePosition = fullscreenScrollPosition / maxFullscreenScroll;

    // Применяем к основному виду
    const mainContentHeight = document.documentElement.scrollHeight;
    const mainViewHeight = window.innerHeight;
    const maxMainScroll = mainContentHeight - mainViewHeight;
    const targetScrollPosition = relativePosition * maxMainScroll;

    console.log('Text-based sync calculation:', {
      relativePosition,
      targetScrollPosition,
      currentScroll: window.scrollY
    });

    if (Math.abs(window.scrollY - targetScrollPosition) > 50) { // Только если разница значительная
      window.scrollTo({
        top: Math.max(0, targetScrollPosition),
        behavior: 'smooth'
      });
    }
  }

  // Fallback метод синхронизации скролла (старый алгоритм)
  private fallbackScrollSync(fullscreenScrollPosition: number): void {
    const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
    if (!fullscreenContainer) return;

    const fullscreenContentHeight = fullscreenContainer.scrollHeight;
    const fullscreenViewHeight = fullscreenContainer.clientHeight;
    const mainContentHeight = document.documentElement.scrollHeight;
    const mainViewHeight = window.innerHeight;

    // Вычисляем относительную позицию (в процентах)
    const maxFullscreenScroll = fullscreenContentHeight - fullscreenViewHeight;
    const relativePosition = maxFullscreenScroll > 0 ? fullscreenScrollPosition / maxFullscreenScroll : 0;

    // Применяем относительную позицию к основному виду
    const maxMainScroll = mainContentHeight - mainViewHeight;
    const targetScrollPosition = relativePosition * maxMainScroll;

    window.scrollTo({
      top: Math.max(0, targetScrollPosition),
      behavior: 'smooth'
    });
  }

  // Находим видимый текстовый элемент в полноэкранном режиме
  private findVisibleTextElement(textContainer: Element, scrollContainer: Element, scrollPosition: number): Element | null {
    const textElements = textContainer.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6');
    const containerRect = scrollContainer.getBoundingClientRect();
    const viewportTop = scrollPosition;
    const viewportBottom = scrollPosition + containerRect.height;

    for (let element of Array.from(textElements)) {
      const htmlElement = element as HTMLElement;
      const elementTop = htmlElement.offsetTop;
      const elementBottom = elementTop + htmlElement.offsetHeight;

      // Проверяем, виден ли элемент в текущей области просмотра
      if (elementBottom > viewportTop && elementTop < viewportBottom) {
        // Возвращаем первый видимый элемент с текстом
        if (htmlElement.textContent && htmlElement.textContent.trim().length > 10) {
          return htmlElement;
        }
      }
    }

    return null;
  }

  // Находим соответствующий элемент в основном виде по содержимому
  private findCorrespondingElement(sourceElement: Element, targetContainer: Element): HTMLElement | null {
    const sourceText = sourceElement.textContent?.trim();
    if (!sourceText || sourceText.length < 10) return null;

    // Берем первые 50 символов для поиска
    const searchText = sourceText.substring(0, 50).trim();

    const targetElements = targetContainer.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6');

    for (let element of Array.from(targetElements)) {
      const htmlElement = element as HTMLElement;
      const elementText = htmlElement.textContent?.trim();

      if (elementText && elementText.includes(searchText)) {
        return htmlElement;
      }
    }

    // Если точное совпадение не найдено, ищем по первым словам
    const firstWords = searchText.split(' ').slice(0, 3).join(' ');
    if (firstWords.length > 5) {
      for (let element of Array.from(targetElements)) {
        const htmlElement = element as HTMLElement;
        const elementText = htmlElement.textContent?.trim();

        if (elementText && elementText.startsWith(firstWords)) {
          return htmlElement;
        }
      }
    }

    return null;
  }

  // Конвертация позиции скролла из основного вида в полноэкранный
  private convertMainScrollToFullscreen(mainScrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    console.log('Converting main scroll to fullscreen:', mainScrollPosition);

    // Используем простой пропорциональный метод для надежности
    this.fallbackMainToFullscreenSync(mainScrollPosition);
  }

  // Fallback метод конвертации из основного вида в полноэкранный
  private fallbackMainToFullscreenSync(mainScrollPosition: number): void {
    const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
    if (!fullscreenContainer) {
      console.log('Fullscreen container not found');
      return;
    }

    const mainContentHeight = document.documentElement.scrollHeight;
    const mainViewHeight = window.innerHeight;
    const fullscreenContentHeight = fullscreenContainer.scrollHeight;
    const fullscreenViewHeight = fullscreenContainer.clientHeight;

    console.log('Sync calculation:', {
      mainScrollPosition,
      mainContentHeight,
      mainViewHeight,
      fullscreenContentHeight,
      fullscreenViewHeight
    });

    // Вычисляем относительную позицию из основного вида (в процентах)
    const maxMainScroll = mainContentHeight - mainViewHeight;
    const relativePosition = maxMainScroll > 0 ? mainScrollPosition / maxMainScroll : 0;

    // Применяем относительную позицию к полноэкранному виду
    const maxFullscreenScroll = fullscreenContentHeight - fullscreenViewHeight;
    const targetFullscreenScrollPosition = relativePosition * maxFullscreenScroll;

    console.log('Setting fullscreen scroll position:', {
      relativePosition,
      targetFullscreenScrollPosition
    });

    this.setFullscreenScrollPosition(Math.max(0, targetFullscreenScrollPosition));
  }

  // Находим видимый текстовый элемент в основном виде
  private findVisibleMainTextElement(textContainer: Element, scrollPosition: number): Element | null {
    const textElements = textContainer.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6');
    const viewportTop = scrollPosition;
    const viewportBottom = scrollPosition + window.innerHeight;

    for (let element of Array.from(textElements)) {
      const htmlElement = element as HTMLElement;
      const rect = htmlElement.getBoundingClientRect();
      const elementTop = scrollPosition + rect.top;
      const elementBottom = elementTop + rect.height;

      // Проверяем, виден ли элемент в текущей области просмотра
      if (elementBottom > viewportTop && elementTop < viewportBottom) {
        // Возвращаем первый видимый элемент с текстом
        if (htmlElement.textContent && htmlElement.textContent.trim().length > 10) {
          return htmlElement;
        }
      }
    }

    return null;
  }

  getAudioDuration(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(url);
      audio.addEventListener("loadedmetadata", () => {
        resolve(audio.duration);
      });
      audio.addEventListener("error", (e) => {
        reject(`Error loading audio: ${e}`);
      });
    });
  }

  play(items: any) {
    const format = items.map(({ chapter, url, time }: ChapterData) => ({
      link: url,
      title: chapter,
      time
    }));
    this.shareDataService.changePlaylist([...format])
  }

  transform(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  prev() {
    if (this.chapter > 0) {
      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTop();
    }
  }

  next() {
    if (this.chapter < this.chapterCount - 1) {
      if(this.chapter == 9 && this.data.paid && !this.libraryService.hasAccess()) {
        this.toasterService.showToast('Вы достигли лимита в 10 страниц для бесплатного просмотра книги. Чтобы продолжить чтение, пожалуйста, оформите подписку.', 'error', 'bottom-middle', 5000)
        return;
      }

      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTop();
    }
  }

  scrollToTop() {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });

    }
  }

  showFullAnnotation: boolean = false;

  toggleAnnotationDisplay() {
    this.showFullAnnotation = !this.showFullAnnotation;
  }

  isInFavourites(id: number) {
    return this.favouriteContent?.includes(id)
  }

  isLiked(id: number) {
    return this.likesContent?.includes(id)
  }

  showBookElement(elementType: string): void {
    this.relatedBooksShowLast = elementType === 'last';
    console.log(this.relatedBooksShowLast, 'this.relatedBooksShowLast');

  }

  openUrl(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link);
    }
  }

  openBook(slug: string, event?: MouseEvent) {
    // Если передан event, проверяем на Ctrl+Click для открытия в новой вкладке
    if (event) {
      if (event.ctrlKey || event.metaKey || event.button === 1 || event.shiftKey) {
        event.preventDefault();
        event.stopPropagation();
        const url = `/${this.translocoService.getActiveLang()}/library/${slug}`;
        if (isPlatformBrowser(this.platformId)) {
          window.open(url, '_blank');
        }
        return;
      }

      // Для обычного клика предотвращаем стандартное поведение
      event.preventDefault();
      event.stopPropagation();
    }

    // SPA навигация с прокруткой вверх и обновлением данных
    this.router.navigate([`${this.translocoService.getActiveLang()}/library/${slug}`]).then((success) => {
      if (success && isPlatformBrowser(this.platformId)) {
        // Прокручиваем к началу страницы
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // Сбрасываем данные похожих книг для обновления
        this.similar = [];
        this.similarCall = false;
      }
    });
  }

  selectChapter(index: any) {
    this.chapter = index;
  }

  secondsToHMS(seconds: any) {
    const numSeconds = Number(seconds);

    // Проверяем на валидность числа
    if (isNaN(numSeconds) || numSeconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const secs = Math.floor(numSeconds % 60);

    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
  }

  // Метод для обновления массива табов в зависимости от наличия аудио
  updateTabs() {
    const baseTabs = ['О книге', 'Читать'];
    if (this.hasAudio) {
      this.tabs = [...baseTabs, 'Аудиокнига'];
    } else {
      this.tabs = baseTabs;
      // Если текущий таб - Аудиокнига, переключаемся на О книге
      if (this.tab === 'Аудиокнига') {
        this.tab = 'О книге';
      }
    }
  }

  // Геттер для проверки наличия аудио
  get hasAudio(): boolean {
    return this.data &&
           this.data.audio &&
           Array.isArray(this.data.audio) &&
           this.data.audio.length > 0;
  }

  navigateToTaggedLibrary(tagId: number) {
    const lang = this.translocoService.getActiveLang();
    this.router.navigate([`/${lang}/library`], {
      queryParams: { tags: tagId }
    });
  }

  playList(items: Track[]) {
    this.shareDataService.changePlaylist([...items])
  }

  // Add to existing class properties
  @ViewChild('fullscreenDialog') fullscreenDialog!: ElementRef<HTMLDialogElement>;

  // Add this method to open the dialog
  openFullscreenDialog() {
    if (isPlatformBrowser(this.platformId)) {
      console.log('Opening fullscreen dialog...');

      // Сохраняем текущую позицию скролла основного вида
      const currentMainScrollPosition = window.scrollY;

      this.isFullscreenBookDialogActive = true;
      this.fullscreenDialog.nativeElement.showModal();

      // Sync current scroll position before opening
      this.syncScrollPositions(false);

      // Ensure we have chapter content for the correct chapter
      const savedPosition = this.getSavedReadingPosition();
      const targetChapter = savedPosition ? savedPosition.chapter : this.chapter;

      console.log('Ensuring chapter content:', {
        currentChapter: this.chapter,
        savedChapter: savedPosition?.chapter,
        targetChapter,
        hasContent: !!this.chapterContent
      });

      if (!this.chapterContent || this.chapter !== targetChapter) {
        console.log('Loading chapter:', targetChapter);
        this.getChapter(targetChapter);
      }

      // Restore fullscreen scroll position after content loads
      setTimeout(() => {
        this.restoreFullscreenScrollPosition(currentMainScrollPosition);
      }, 500); // Увеличиваем задержку для надежности
    }
  }

  // Восстановление позиции скролла в полноэкранном режиме
  private restoreFullscreenScrollPosition(fallbackMainScrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId) || !this.data?.id) {
      console.log('Cannot restore fullscreen position: browser check failed');
      return;
    }

    const savedPosition = this.getSavedReadingPosition();

    console.log('Restoring fullscreen position:', {
      bookId: this.data.id,
      bookCode: this.bookId,
      savedPosition,
      fallbackMainScrollPosition,
      currentChapter: this.chapter,
      chapterContent: !!this.chapterContent
    });

    // Ждем, пока контент полностью загрузится в полноэкранном режиме
    const waitForFullscreenContent = (attempts: number = 0) => {
      if (attempts > 50) { // Максимум 5 секунд ожидания
        console.log('Timeout waiting for fullscreen content after', attempts, 'attempts');
        return;
      }

      const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
      const fullscreenContent = this.fullscreenDialog?.nativeElement.querySelector('text-interaction');

      console.log('Waiting for content, attempt:', attempts, {
        fullscreenContainer: !!fullscreenContainer,
        fullscreenContent: !!fullscreenContent,
        scrollHeight: fullscreenContainer?.scrollHeight,
        clientHeight: fullscreenContainer?.clientHeight,
        chapterContent: !!this.chapterContent
      });

      if (!fullscreenContainer || !fullscreenContent || !this.chapterContent) {
        setTimeout(() => waitForFullscreenContent(attempts + 1), 100);
        return;
      }

      // Если контент короткий и не требует скролла, все равно пытаемся восстановить позицию
      if (fullscreenContainer.scrollHeight <= fullscreenContainer.clientHeight) {
        console.log('Content is short, no scroll needed, but proceeding with position restore');
      }

      console.log('Fullscreen content loaded, restoring position');

      // Проверяем, что мы на правильной главе
      if (savedPosition && this.chapter !== savedPosition.chapter) {
        console.log('Wrong chapter loaded, expected:', savedPosition.chapter, 'actual:', this.chapter);
        // Загружаем правильную главу и повторяем попытку
        this.getChapter(savedPosition.chapter);
        setTimeout(() => {
          this.restoreFullscreenScrollPosition(fallbackMainScrollPosition);
        }, 500);
        return;
      }

      // Подробное логирование сохраненной позиции
      console.log('Detailed savedPosition analysis:', {
        savedPosition,
        hasFullscreenPosition: savedPosition && savedPosition.fullscreenScrollPosition > 0,
        fullscreenScrollPosition: savedPosition?.fullscreenScrollPosition,
        fallbackMainScrollPosition,
        hasFallback: fallbackMainScrollPosition > 0
      });

      // Всегда пытаемся восстановить сохраненную позицию полноэкранного режима
      if (savedPosition && savedPosition.fullscreenScrollPosition > 0) {
        console.log('Using saved fullscreen position:', savedPosition.fullscreenScrollPosition);
        this.setFullscreenScrollPosition(savedPosition.fullscreenScrollPosition);

        // Проверяем, что позиция действительно установилась
        setTimeout(() => {
          const actualPosition = this.getFullscreenScrollPosition();
          console.log('Position after setting:', {
            requested: savedPosition.fullscreenScrollPosition,
            actual: actualPosition
          });
        }, 100);
      } else if (fallbackMainScrollPosition > 0) {
        // Конвертируем текущую позицию основного вида в полноэкранную
        console.log('Converting main scroll to fullscreen:', fallbackMainScrollPosition);
        this.convertMainScrollToFullscreen(fallbackMainScrollPosition);
      } else {
        console.log('No position to restore - reasons:', {
          noSavedPosition: !savedPosition,
          noFullscreenPosition: !savedPosition?.fullscreenScrollPosition || savedPosition.fullscreenScrollPosition <= 0,
          noFallbackPosition: fallbackMainScrollPosition <= 0
        });
      }
    };

    waitForFullscreenContent();
  }

  // Add this method to close the dialog
  closeFullscreenDialog() {
    if (!isPlatformBrowser(this.platformId)) return;

    // Get current fullscreen scroll position and chapter before closing
    const fullscreenScrollPosition = this.getFullscreenScrollPosition();
    const currentChapter = this.chapter;

    console.log('Closing fullscreen dialog:', {
      chapter: currentChapter,
      scrollPosition: fullscreenScrollPosition
    });

    // Save current fullscreen position before closing
    if (this.data?.id) {
      this.readingPositionService.saveReadingPosition(
        this.data.id,
        this.bookId,
        currentChapter,
        0, // normal scroll will be set later
        fullscreenScrollPosition,
        this.chapterCount,
        'fullscreen' // сохраняем как последнюю активность в полноэкранном режиме
      );
    }

    this.isFullscreenBookDialogActive = false;
    this.fullscreenDialog.nativeElement.close();

    // After closing, ensure main view shows the same chapter and scroll position
    setTimeout(() => {
      // Switch to reading tab if not already there
      if (this.tab !== 'Читать') {
        this.tab = 'Читать';
      }

      // Load the same chapter that was in fullscreen if needed
      if (!this.chapterContent || this.chapter !== currentChapter) {
        this.getChapter(currentChapter);
      }

      // Scroll to corresponding position after content is loaded
      setTimeout(() => {
        if (fullscreenScrollPosition > 0) {
          this.scrollToCorrespondingPosition(fullscreenScrollPosition);
        }
      }, 300);
    }, 100);
  }











  showScrollTop: boolean = false;
  similarCall: boolean = false;
  private scrollSaveTimeout: any = null;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (isPlatformBrowser(this.platformId)) {
      this.showScrollTop = window.scrollY > 300;
      if(this.showScrollTop){

          if(this.similar.length === 0 && !this.similarCall) {
            this.similarCall = true;
            this.libraryService.getSimilar(this.bookId).subscribe((res: any) => {
              this.similar = res;
            });
          }
      }

      // Сохраняем позицию скролла с задержкой (debounce)
      if (this.tab === 'Читать' && this.chapterContent && !this.isFullscreenBookDialogActive) {
        if (this.scrollSaveTimeout) {
          clearTimeout(this.scrollSaveTimeout);
        }
        this.scrollSaveTimeout = setTimeout(() => {
          this.saveReadingPosition(this.chapter);
        }, 1000); // Сохраняем через 1 секунду после остановки скролла
      }
    }
  }

  // Обработчик скролла для полноэкранного режима
  onFullscreenScroll(event: Event) {
    if (isPlatformBrowser(this.platformId) && this.isFullscreenBookDialogActive && this.chapterContent && this.data?.id) {
      if (this.fullscreenScrollSaveTimeout) {
        clearTimeout(this.fullscreenScrollSaveTimeout);
      }
      this.fullscreenScrollSaveTimeout = setTimeout(() => {
        const fullscreenScrollPosition = this.getFullscreenScrollPosition();

        console.log('Saving fullscreen scroll position from scroll event:', {
          bookId: this.data.id,
          chapter: this.chapter,
          fullscreenScrollPosition
        });

        // Сохраняем полную позицию чтения включая главу и скролл
        this.readingPositionService.saveReadingPosition(
          this.data.id,
          this.bookId,
          this.chapter,
          0, // normal scroll position (not used in fullscreen)
          fullscreenScrollPosition,
          this.chapterCount,
          'fullscreen' // указываем, что последняя активность была в полноэкранном режиме
        );
      }, 1000);
    }
  }

  // Тестовый метод для принудительного сохранения позиции (для отладки)
  testSaveFullscreenPosition() {
    if (isPlatformBrowser(this.platformId) && this.isFullscreenBookDialogActive && this.data?.id) {
      const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
      const fullscreenScrollPosition = this.getFullscreenScrollPosition();

      console.log('TEST: Current fullscreen state:', {
        bookId: this.data.id,
        chapter: this.chapter,
        fullscreenScrollPosition,
        scrollHeight: fullscreenContainer?.scrollHeight,
        clientHeight: fullscreenContainer?.clientHeight,
        maxScroll: fullscreenContainer ? fullscreenContainer.scrollHeight - fullscreenContainer.clientHeight : 0
      });

      // Если контент короткий, принудительно устанавливаем тестовую позицию
      let testPosition = fullscreenScrollPosition;
      if (fullscreenContainer && fullscreenContainer.scrollHeight <= fullscreenContainer.clientHeight) {
        testPosition = 500; // Тестовая позиция
        console.log('TEST: Content is short, using test position:', testPosition);
      }

      console.log('TEST: Manually saving fullscreen position:', {
        bookId: this.data.id,
        chapter: this.chapter,
        originalPosition: fullscreenScrollPosition,
        testPosition
      });

      this.readingPositionService.saveReadingPosition(
        this.data.id,
        this.bookId,
        this.chapter,
        0,
        testPosition,
        this.chapterCount,
        'fullscreen'
      );

      // Проверяем, что сохранилось
      setTimeout(() => {
        const saved = this.getSavedReadingPosition();
        console.log('TEST: Position after manual save:', saved);
      }, 100);
    }
  }

  // Тестовый метод для принудительного скролла (для отладки)
  testScrollFullscreen() {
    if (isPlatformBrowser(this.platformId) && this.isFullscreenBookDialogActive) {
      const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
      if (fullscreenContainer) {
        console.log('TEST: Forcing scroll to position 300');
        fullscreenContainer.scrollTop = 300;

        setTimeout(() => {
          const actualPosition = this.getFullscreenScrollPosition();
          console.log('TEST: Position after forced scroll:', actualPosition);
        }, 100);
      }
    }
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -260,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 260,
        behavior: 'smooth'
      });
    }
  }

  // Navigation methods for fullscreen mode
  nextFullscreen() {
    if (this.chapter < this.chapterCount - 1) {
      // Save current fullscreen scroll position before changing chapter
      this.syncScrollPositions(true);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTopFullscreen();
    }
  }

  prevFullscreen() {
    if (this.chapter > 0) {
      // Save current fullscreen scroll position before changing chapter
      this.syncScrollPositions(true);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTopFullscreen();
    }
  }

  goToChapterFullscreen(chapterIndex: number) {
    // Save current fullscreen scroll position before changing chapter
    this.syncScrollPositions(true);
    this.chapterContent = ''; // Clear content to show loading
    this.getChapter(chapterIndex);
    this.fullscreenContents = false; // Close contents after selection
    this.scrollToTopFullscreen();
  }

  scrollToTopFullscreen() {
    if (isPlatformBrowser(this.platformId)) {
      // Scroll to top of the fullscreen dialog content
      const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
      if (dialogContent) {
        dialogContent.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (isPlatformBrowser(this.platformId)) {
      const target = event.target as HTMLElement;
      const settingsContainer = document.querySelector('.settings-container');

      // Если клик вне settings-container, закрываем меню
      if (settingsContainer && !settingsContainer.contains(target)) {
        this.showFullscreenOptions = false;
      }
    }
  }

  // Метод для получения стилей размера шрифта
  getFullscreenFontSizeStyles() {
    const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);
    return {
      'font-size': this.fullscreenBookFontSize + 'px',
      'line-height': lineHeight + 'px',
      '--fullscreen-font-size': this.fullscreenBookFontSize + 'px',
      '--fullscreen-line-height': lineHeight + 'px'
    };
  }

  // Метод для получения стилей ширины текста
  getFullscreenTextWidthStyles() {
    const widthMap = {
      'narrow': '700px',
      'medium': '930px',
      'full': '100%'
    };
    return {
      'max-width': widthMap[this.fullscreenBookTextWidth as keyof typeof widthMap],
      'margin': '0 auto'
    };
  }

  // Метод для применения стилей через JavaScript
  applyFullscreenFontSize() {
    if (isPlatformBrowser(this.platformId)) {
      setTimeout(() => {
        const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);

        // Находим все элементы текста внутри text-interaction
        const textElements = document.querySelectorAll('.fullscreen-book-dialog text-interaction *');

        textElements.forEach((element: any) => {
          // Сохраняем текущий font-weight перед изменением размера
          const currentFontWeight = window.getComputedStyle(element).fontWeight;
          element.style.fontSize = this.fullscreenBookFontSize + 'px';
          element.style.lineHeight = lineHeight + 'px';
          element.style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          element.style.setProperty('line-height', lineHeight + 'px', 'important');
          // Восстанавливаем font-weight
          if (currentFontWeight && currentFontWeight !== 'normal') {
            element.style.setProperty('font-weight', currentFontWeight, 'important');
          }
        });

        // Также применяем к самому text-interaction
        const textInteraction = document.querySelector('.fullscreen-book-dialog text-interaction');
        if (textInteraction) {
          const currentFontWeight = window.getComputedStyle(textInteraction).fontWeight;
          (textInteraction as HTMLElement).style.fontSize = this.fullscreenBookFontSize + 'px';
          (textInteraction as HTMLElement).style.lineHeight = lineHeight + 'px';
          (textInteraction as HTMLElement).style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          (textInteraction as HTMLElement).style.setProperty('line-height', lineHeight + 'px', 'important');
          if (currentFontWeight && currentFontWeight !== 'normal') {
            (textInteraction as HTMLElement).style.setProperty('font-weight', currentFontWeight, 'important');
          }
        }
      }, 500);
    }
  }


}

interface ChapterData {
  chapter: string;
  url: string;
  time: number
}
