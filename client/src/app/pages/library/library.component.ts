import {Component, inject, PLATFORM_ID, signal, DestroyRef, effect, ViewChild, ElementRef, HostListener, OnD<PERSON>roy} from '@angular/core';
import {CommonModule, isPlatformBrowser, NgOptimizedImage} from "@angular/common";
import {LibraryService} from "@/services/library.service";
import {ReadingPositionService} from "@/services/reading-position.service";
import {DomSanitizer, Meta, Title} from "@angular/platform-browser";
import {ActivatedRoute, Router} from "@angular/router";
import {environment} from "@/env/environment";
import {ProfileService} from "@/services/profile.service";
import {ShareDataService} from "@/services/share-data.service";
import { ToasterService } from '@/services/toaster.service';
import {AdvertisingService} from "@/services/advertising.service";
import {TranslocoModule, TranslocoService} from "@jsverse/transloco";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthService } from '@/services/auth.service';
import {FormsModule} from "@angular/forms";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { TextInteractionComponent } from '../content/text-interaction/text-interaction.component';
import { Track } from '@/interfaces/track';
import { ClickOutsideDirective } from '@/directives/clickOutside';
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component';

@Component({
  selector: 'app-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    TextInteractionComponent,
    TranslocoModule,
    ClickOutsideDirective,
    LoadingIndicatorComponent
  ],
  templateUrl: './library.component.html',
  styleUrl: './library.component.scss'
})
export class LibraryComponent implements OnDestroy {
  shareDataService = inject(ShareDataService)
  profileService = inject(ProfileService);
  libraryService = inject(LibraryService);
  readingPositionService = inject(ReadingPositionService);
  authService = inject(AuthService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  platformId = inject(PLATFORM_ID)
  titleService = inject(Title)
  metaService = inject(Meta);
  advertisingService = inject(AdvertisingService);
  translocoService = inject(TranslocoService);
  auth = inject(AuthService)
  sanitizer = inject(DomSanitizer)
  code = this.route.snapshot.paramMap.get('code');
  selection: any = signal(null)
  toasterService = inject(ToasterService);
  quote: string | null = null
  id: number | null = null
  rendition: any = null
  data: any = null
  currentPage: any
  tabs = ['О книге', 'Читать', 'Аудиокнига']
  tab = 'О книге'
  descriptionTab = 'annotation'
  bookLoaded = false;
  bookId = ""
  private readonly destroyRef = inject(DestroyRef);
  chapter = 0
  theme = 'light'
  fontSize = '17'
  fontFamily = 'Arial'
  audioBookIndex = -1;
  chapterContent: any = ''
  chapterCount: number = 0
  chapterTitles: any = []
  chapterReadingTime: number = 0

  likesContent: any = [];
  favouriteContent: any = [];
  likesCount: number = 0;
  lang = 'ru';
  relatedBooksShowLast: boolean = false;
  relatedBooks = [
    {
      name: 'Панчадаши',
      author: 'Шри Видьяранья Свами',
      bookImgSrc: 'assets/images/book_1.webp'
    },
    {
      name: 'Аватары Господа Шивы',
      author: 'Всемирная Община Санатана Дхармы',
      bookImgSrc: 'assets/images/book_2.webp'
    },
    {
      name: 'Сборник лекций по Шайва-сиддханте',
      author: 'Шри Гуру Свами Вишнудевананда Гири',
      bookImgSrc: 'assets/images/book_3.webp'
    },
  ];
  fullscreenBookTheme = 'light';
  fullscreenBookFont = 'Prata';
  fullscreenBookFontSize: number = 24; // Will be set to responsive default in constructor
  fullscreenBookTextWidth = 'narrow'; // Default to narrow column (700px)
  isFullscreenBookDialogActive: boolean = false;
  showFullscreenOptions: boolean = false;
  similar: any = []
  contents: boolean = false;
  fullscreenContents: boolean = false;
  durationMinutes: any = ''

  // Reading position synchronization properties
  private fullscreenScrollSaveTimeout: any = null;
  private lastSyncedScrollPosition: number = 0;
  // private touchStartX: number = 0;
  // private touchEndX: number = 0;
  // private mouseStartX: number = 0;
  // private mouseDown: boolean = false;
  // private readonly swipeThreshold: number = 30;

  quoteId = this.route.snapshot.queryParams['quoteId']
  libraryQuote: any = null

  constructor() {
    // Set responsive default font size
    this.fullscreenBookFontSize = this.getResponsiveDefaultFontSize();

    // Add window resize listener to update font size responsively
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('resize', () => {
        // Only update to responsive default if user hasn't manually changed font size
        const currentDefault = this.getResponsiveDefaultFontSize();
        if (this.fullscreenBookFontSize === 24 || this.fullscreenBookFontSize === 18) {
          this.fullscreenBookFontSize = currentDefault;
          this.applyFullscreenFontSize();
        }
      });
    }

    effect(() => {
      if(this.profileService.name() && this.profileService.profile) {
        this.setCount();
      }
    });
  }

  // Get responsive default font size based on screen width
  getResponsiveDefaultFontSize(): number {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth <= 768) {
        return 18; // Smaller font for tablet and mobile
      }
    }
    return 24; // Default large font for desktop
  }

  // Calculate proportional line-height based on font size
  calculateProportionalLineHeight(fontSize: number): number {
    return Math.round(fontSize * 1.5); // 1.5 ratio for optimal readability
  }

  ngOnInit() {
    if(this.authService.isAuth){
      this.libraryService.getLikes(this.route.snapshot.params['code']).subscribe((res: any) => {
        this.likesContent = res[0];
        this.likesCount = res[1];
      })
      this.libraryService.getFavourites().subscribe((res: any) => {
        // Проверяем, что res это массив или объект с массивом
        if (Array.isArray(res)) {
          this.favouriteContent = res.map((e: any) => e.id);
        } else if (res && Array.isArray(res.items)) {
          this.favouriteContent = res.items.map((e: any) => e.id);
        } else {
          this.favouriteContent = [];
        }
      })
    }

    // if(this.profileService.name() && this.profileService.profile) {
    //   this.setCount();
    // } else {
    //   this.profileService.setProfile();
    // }

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((lang: any) => {
      if(this.lang === lang) {
        return;
      } else {
        this.lang = lang;
      }
      this.getDate();
    })

    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      const newBookId = params['code'];
      if (newBookId !== this.bookId) {
        // Сбрасываем данные при смене книги
        this.similar = [];
        this.similarCall = false;
        this.tab = 'О книге'; // Возвращаемся на первый таб
        this.chapter = 0; // Сбрасываем главу

        if (isPlatformBrowser(this.platformId)) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
      this.bookId = newBookId;
      this.getDate();
    })

    // Handle query parameters for tab selection
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(queryParams => {
      const tabParam = queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.tab = tabParam;

        // If switching to reading tab, load the first chapter
        if (tabParam === 'Читать') {
          if (this.quoteId) {
            this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
              this.libraryQuote = res;
              this.chapter = res.page;
              this.getChapter(res.page);
            })
          } else {
            // Проверяем сохраненную позицию чтения
            const savedPosition = this.getSavedReadingPosition();
            const startChapter = savedPosition !== null ? savedPosition.chapter : 0;
            this.getChapter(startChapter, savedPosition?.scrollPosition);
          }
        }
      }
    })
  }

  ngOnDestroy() {
    // Очищаем timeout при уничтожении компонента
    if (this.scrollSaveTimeout) {
      clearTimeout(this.scrollSaveTimeout);
    }
  }

  get duration() {
    if(!this.data || !this.data.duration) return 0
    const [hours, minutes, seconds] = this.data.duration.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + Math.round(seconds / 60);
    const formattedHours = Math.floor(totalMinutes / 60);
    const formattedMinutes = totalMinutes % 60;
    return `${formattedHours} ч. ${formattedMinutes} мин.`;
  }

  setCount() {
    this.likesContent = this.profileService.profile!.libraryLikes;
    this.favouriteContent = this.profileService.profile!.libraryFavourites;
  }

  getDate(views = true) {
    if(this.bookId)
    this.libraryService.get(this.bookId, views).subscribe((res: any) => {
      this.id = res.id
      this.titleService.setTitle(res.seo_title);
      this.metaService.updateTag({name: 'description', content: res.seo_description})
      this.data = res;

      // Безопасный расчет времени аудио
      if (this.data.audio && Array.isArray(this.data.audio) && this.data.audio.length > 0) {
        const totalSeconds = this.data.audio.reduce((a: number, b: any) => {
          const duration = Number(b.duration);
          return a + (isNaN(duration) ? 0 : duration);
        }, 0);
        this.durationMinutes = Math.ceil(totalSeconds / 60);
      } else {
        this.durationMinutes = 0;
      }

      // Обновляем массив табов в зависимости от наличия аудио
      this.updateTabs();

      // Handle tab selection from query parameters after tabs are updated
      const tabParam = this.route.snapshot.queryParams['tab'];
      if (tabParam && this.tabs.includes(tabParam)) {
        this.changeTab(tabParam);
      } else if(this.quoteId) {
        this.changeTab('Читать');
      }

      // this.data.audio.forEach((el: any) => {
      //   this.getAudioDuration(el.url).then(duration => {
      //     el.time = this.formatDuration(duration)
      //   })
      //   .catch(error => console.error(error));
      // });
    })
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return [
      hours > 0 ? String(hours).padStart(2, "0") : "00",
      String(minutes).padStart(2, "0"),
      String(secs).padStart(2, "0")
    ].join(":");
  }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/library/${content.code}`).then(() =>
        this.toasterService.showToast('Ссылка на книгу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }


  favorite(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(id).subscribe({
      next: (r) => {
        if(this.favouriteContent.includes(id)) {
          this.favouriteContent.splice(this.favouriteContent.indexOf(id), 1)
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.favouriteContent.push(id)
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  like(id: number) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(id).subscribe({
      next: (r) => {

        if(this.likesContent.includes(id)) {
          this.likesContent.splice(this.likesContent.indexOf(id), 1)
          this.likesCount--;
          this.toasterService.showToast('Книга удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        } else {
          this.likesContent.push(id)
          this.likesCount++;
          this.toasterService.showToast('Книга добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        }

      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  get likes () {
    return this.likesContent.filter((item: any) => item.id === this.data.id);
  }

  changeFontSize(e: Event) {
    const target = e.target as HTMLInputElement;
    if (this.rendition) {
      // Create a style rule that targets all text elements
      const fontRule = `* { font-size: ${target.value}px !important; }`;

      // Register and apply new theme with our font size
      this.rendition.themes.register('custom-size', {
        body: {
          'font-size': `${target.value}px !important`,
        },
        'p, div, span, h1, h2, h3, h4, h5, h6': {
          'font-size': `${target.value}px !important`,
        }
      });

      // Add direct stylesheet injection
      const doc = this.rendition.getContents()[0].document;
      let style = doc.createElement('style');
      style.innerHTML = fontRule;
      doc.head.appendChild(style);

      // Select our custom theme
      this.rendition.themes.select('custom-size');
    }
  }

  changeTheme(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.select(target.value)
    this.rendition.display((document.querySelectorAll('.library-content select option')[1] as HTMLOptionElement).getAttribute('ref'))
    this.rendition.display((document.querySelectorAll('.library-content select option')[0] as HTMLOptionElement).getAttribute('ref'))
  }

  changeFont(e: Event) {
    const target = e.target as HTMLSelectElement;
    this.rendition.themes.override("font-family", target.value);
    //this.rendition.themes.font(`${target.value}!important`)
  }

  showQuoteContextMenu(e: MouseEvent) {
    // const selection = window.parent.document.querySelector('iframe')!.contentWindow!.getSelection
    // const offsetTop = (window.parent.document.getElementById('epub')!.offsetParent as HTMLElement).offsetTop + 93 + e.pageY;
    // (document.querySelector('.library-context') as HTMLDivElement).style.left = `${e.screenX}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.top = `${offsetTop}px`;
    // (document.querySelector('.library-context') as HTMLDivElement).style.display = `block`
    // if(selection()) {
    //   this.selection.set(selection()!.toString())
    //   this.quote = selection()!.toString()
    // }
    return false
  }

  addQuoteToFavourites() {
    if(!this.id || !this.quote) return
    this.hideContextMenu()
    this.libraryService.addQuoteToFavourites(this.id, this.quote, this.currentPage).subscribe((res: any) => {
      this.quote = null
      navigator.clipboard.writeText(environment.baseUrl + '/ru/library/' + this.code + '?quoteId=' + res).then(() => {
        this.hideContextMenu()
      })
    })
  }

  hideContextMenu() {
    (document.querySelector('.library-context') as HTMLDivElement).style.display = `none`
  }

  copySelection(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      navigator.clipboard.writeText(this.selection()).then(() => {
        alert('Copied to clipboard')
        this.hideContextMenu()
      })
    }
  }

  shareQuote(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      this.addQuoteToFavourites()
    }
  }

  getTags() {
    return this.data.tags.map((e: any) => e.name).join(', ');
  }

  changeTab(tab: string) {
    this.tab = tab;

    if(tab == 'Читать') {
      if(this.quoteId) {
        this.libraryService.getQuote(this.quoteId).subscribe((res: any) => {
          this.libraryQuote = res;
          this.chapter = res.page;
          this.getChapter(res.page);
        })
      } else {
        // Проверяем сохраненную позицию чтения
        const savedPosition = this.getSavedReadingPosition();
        const startChapter = savedPosition !== null ? savedPosition.chapter : 0;
        this.getChapter(startChapter, savedPosition?.scrollPosition);
      }
    }
  }

  getChapter(index: number, scrollPosition?: number) {
    this.chapter = index; // Обновляем текущую главу
    this.libraryService.getChapter(this.data.id, index).subscribe((res: any) => {
      this.chapterContent = res.content.content;
      this.chapterCount = res.count;
      this.chapterTitles = res.titles;

      // Рассчитываем время чтения для текущей главы
      this.chapterReadingTime = this.calculateReadingTime(this.chapterContent);

      // Сохраняем позицию чтения
      this.saveReadingPosition(index);

      if(this.libraryQuote && this.libraryQuote.page == index) {
        this.chapterContent = this.chapterContent.replace(this.libraryQuote.quote.trim(), `<span class="quote" style="background: var(--selection); color: black;">${this.libraryQuote.quote.trim()}</span>`)

        setTimeout(() => {
          const element: HTMLElement = document.querySelector('.quote')!;
          if(element) {
            window.scrollTo({
              top: element.offsetTop + 100,
              behavior: 'smooth'
            });
          }
        }, 500)

      } else if (scrollPosition !== undefined && scrollPosition > 0) {
        // Восстанавливаем позицию скролла если она была сохранена
        setTimeout(() => {
          if (isPlatformBrowser(this.platformId)) {
            if (this.isFullscreenBookDialogActive) {
              // Restore fullscreen scroll position
              const savedPosition = this.getSavedReadingPosition();
              if (savedPosition && savedPosition.fullscreenScrollPosition > 0) {
                this.setFullscreenScrollPosition(savedPosition.fullscreenScrollPosition);
              }
            } else {
              // Restore normal scroll position
              window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
              });
            }
          }
        }, 500);
      }

    })
  }

  // Метод для расчета времени чтения
  calculateReadingTime(htmlContent: string): number {
    if (!htmlContent) return 0;

    // Удаляем HTML теги и получаем чистый текст
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Считаем слова
    const words = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;

    // Средняя скорость чтения 225 слов в минуту
    const wordsPerMinute = 225;
    return Math.ceil(words / wordsPerMinute);
  }

  // Сохранение позиции чтения
  private saveReadingPosition(chapter: number): void {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      const scrollPosition = window.scrollY;
      const fullscreenScrollPosition = this.getFullscreenScrollPosition();

      this.readingPositionService.saveReadingPosition(
        this.data.id,
        this.bookId,
        chapter,
        scrollPosition,
        fullscreenScrollPosition,
        this.chapterCount
      );
    }
  }

  // Получение сохраненной позиции чтения
  private getSavedReadingPosition(): { chapter: number; scrollPosition: number; fullscreenScrollPosition: number } | null {
    if (isPlatformBrowser(this.platformId) && this.data?.id) {
      const savedPosition = this.readingPositionService.getReadingPosition(this.data.id, this.bookId);

      if (savedPosition) {
        // Показываем тостер о восстановлении позиции
        if (savedPosition.chapter > 0) {
          this.toasterService.showToast(
            `Возвращено к последней позиции чтения (страница ${savedPosition.chapter + 1})`,
            'success',
            'bottom-middle',
            3000
          );
        }
        return {
          chapter: savedPosition.chapter,
          scrollPosition: savedPosition.scrollPosition || 0,
          fullscreenScrollPosition: savedPosition.fullscreenScrollPosition || 0
        };
      }
    }
    return null;
  }

  // Очистка старых позиций чтения (старше 30 дней)
  private cleanOldReadingPositions(readingPositions: any): void {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    Object.keys(readingPositions).forEach(bookId => {
      const position = readingPositions[bookId];
      if (position.timestamp && position.timestamp < thirtyDaysAgo) {
        delete readingPositions[bookId];
      }
    });
  }

  // Получение текущей позиции скролла в полноэкранном режиме
  private getFullscreenScrollPosition(): number {
    if (!isPlatformBrowser(this.platformId) || !this.fullscreenDialog) return 0;

    const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
    return dialogContent ? dialogContent.scrollTop : 0;
  }

  // Установка позиции скролла в полноэкранном режиме
  private setFullscreenScrollPosition(scrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId) || !this.fullscreenDialog) return;

    const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
    if (dialogContent) {
      dialogContent.scrollTop = scrollPosition;
    }
  }

  // Синхронизация позиций скролла между обычным и полноэкранным режимами
  private syncScrollPositions(fromFullscreen: boolean = false): void {
    if (!isPlatformBrowser(this.platformId) || !this.data?.id) return;

    if (fromFullscreen) {
      // Синхронизация из полноэкранного режима в обычный
      const fullscreenScrollPosition = this.getFullscreenScrollPosition();
      this.readingPositionService.updateScrollPosition(this.data.id, fullscreenScrollPosition, true);
    } else {
      // Синхронизация из обычного режима в полноэкранный
      const normalScrollPosition = window.scrollY;
      this.readingPositionService.updateScrollPosition(this.data.id, normalScrollPosition, false);
    }
  }

  // Прокрутка основного вида к соответствующей позиции из полноэкранного режима
  private scrollToCorrespondingPosition(fullscreenScrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Ждем, пока контент загрузится в основном виде
    const waitForContent = () => {
      const mainTextContainer = document.querySelector('.book_text_section');
      if (!mainTextContainer || !this.chapterContent) {
        // Если контент еще не загружен, ждем еще немного
        setTimeout(waitForContent, 100);
        return;
      }

      // Получаем размеры контейнеров
      const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
      if (!fullscreenContainer) return;

      const fullscreenContentHeight = fullscreenContainer.scrollHeight;
      const fullscreenViewHeight = fullscreenContainer.clientHeight;
      const mainContentHeight = document.documentElement.scrollHeight;
      const mainViewHeight = window.innerHeight;

      // Вычисляем относительную позицию (в процентах)
      const maxFullscreenScroll = fullscreenContentHeight - fullscreenViewHeight;
      const relativePosition = maxFullscreenScroll > 0 ? fullscreenScrollPosition / maxFullscreenScroll : 0;

      // Применяем относительную позицию к основному виду
      const maxMainScroll = mainContentHeight - mainViewHeight;
      const targetScrollPosition = relativePosition * maxMainScroll;

      // Плавно прокручиваем к целевой позиции
      window.scrollTo({
        top: Math.max(0, targetScrollPosition),
        behavior: 'smooth'
      });
    };

    waitForContent();
  }

  // Конвертация позиции скролла из основного вида в полноэкранный
  private convertMainScrollToFullscreen(mainScrollPosition: number): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Получаем размеры контейнеров
    const fullscreenContainer = this.fullscreenDialog?.nativeElement.querySelector('.book-pages-container');
    if (!fullscreenContainer) return;

    const mainContentHeight = document.documentElement.scrollHeight;
    const mainViewHeight = window.innerHeight;
    const fullscreenContentHeight = fullscreenContainer.scrollHeight;
    const fullscreenViewHeight = fullscreenContainer.clientHeight;

    // Вычисляем относительную позицию из основного вида (в процентах)
    const maxMainScroll = mainContentHeight - mainViewHeight;
    const relativePosition = maxMainScroll > 0 ? mainScrollPosition / maxMainScroll : 0;

    // Применяем относительную позицию к полноэкранному виду
    const maxFullscreenScroll = fullscreenContentHeight - fullscreenViewHeight;
    const targetFullscreenScrollPosition = relativePosition * maxFullscreenScroll;

    // Устанавливаем позицию в полноэкранном режиме
    this.setFullscreenScrollPosition(Math.max(0, targetFullscreenScrollPosition));
  }

  getAudioDuration(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(url);
      audio.addEventListener("loadedmetadata", () => {
        resolve(audio.duration);
      });
      audio.addEventListener("error", (e) => {
        reject(`Error loading audio: ${e}`);
      });
    });
  }

  play(items: any) {
    const format = items.map(({ chapter, url, time }: ChapterData) => ({
      link: url,
      title: chapter,
      time
    }));
    this.shareDataService.changePlaylist([...format])
  }

  transform(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  prev() {
    if (this.chapter > 0) {
      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTop();
    }
  }

  next() {
    if (this.chapter < this.chapterCount - 1) {
      if(this.chapter == 9 && this.data.paid && !this.libraryService.hasAccess()) {
        this.toasterService.showToast('Вы достигли лимита в 10 страниц для бесплатного просмотра книги. Чтобы продолжить чтение, пожалуйста, оформите подписку.', 'error', 'bottom-middle', 5000)
        return;
      }

      // Сохраняем текущую позицию скролла перед переходом
      this.saveReadingPosition(this.chapter);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTop();
    }
  }

  scrollToTop() {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });

    }
  }

  showFullAnnotation: boolean = false;

  toggleAnnotationDisplay() {
    this.showFullAnnotation = !this.showFullAnnotation;
  }

  isInFavourites(id: number) {
    return this.favouriteContent?.includes(id)
  }

  isLiked(id: number) {
    return this.likesContent?.includes(id)
  }

  showBookElement(elementType: string): void {
    this.relatedBooksShowLast = elementType === 'last';
    console.log(this.relatedBooksShowLast, 'this.relatedBooksShowLast');

  }

  openUrl(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link);
    }
  }

  openBook(slug: string, event?: MouseEvent) {
    // Если передан event, проверяем на Ctrl+Click для открытия в новой вкладке
    if (event) {
      if (event.ctrlKey || event.metaKey || event.button === 1 || event.shiftKey) {
        event.preventDefault();
        event.stopPropagation();
        const url = `/${this.translocoService.getActiveLang()}/library/${slug}`;
        if (isPlatformBrowser(this.platformId)) {
          window.open(url, '_blank');
        }
        return;
      }

      // Для обычного клика предотвращаем стандартное поведение
      event.preventDefault();
      event.stopPropagation();
    }

    // SPA навигация с прокруткой вверх и обновлением данных
    this.router.navigate([`${this.translocoService.getActiveLang()}/library/${slug}`]).then((success) => {
      if (success && isPlatformBrowser(this.platformId)) {
        // Прокручиваем к началу страницы
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // Сбрасываем данные похожих книг для обновления
        this.similar = [];
        this.similarCall = false;
      }
    });
  }

  selectChapter(index: any) {
    this.chapter = index;
  }

  secondsToHMS(seconds: any) {
    const numSeconds = Number(seconds);

    // Проверяем на валидность числа
    if (isNaN(numSeconds) || numSeconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const secs = Math.floor(numSeconds % 60);

    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
  }

  // Метод для обновления массива табов в зависимости от наличия аудио
  updateTabs() {
    const baseTabs = ['О книге', 'Читать'];
    if (this.hasAudio) {
      this.tabs = [...baseTabs, 'Аудиокнига'];
    } else {
      this.tabs = baseTabs;
      // Если текущий таб - Аудиокнига, переключаемся на О книге
      if (this.tab === 'Аудиокнига') {
        this.tab = 'О книге';
      }
    }
  }

  // Геттер для проверки наличия аудио
  get hasAudio(): boolean {
    return this.data &&
           this.data.audio &&
           Array.isArray(this.data.audio) &&
           this.data.audio.length > 0;
  }

  navigateToTaggedLibrary(tagId: number) {
    const lang = this.translocoService.getActiveLang();
    this.router.navigate([`/${lang}/library`], {
      queryParams: { tags: tagId }
    });
  }

  playList(items: Track[]) {
    this.shareDataService.changePlaylist([...items])
  }

  // Add to existing class properties
  @ViewChild('fullscreenDialog') fullscreenDialog!: ElementRef<HTMLDialogElement>;

  // Add this method to open the dialog
  openFullscreenDialog() {
    if (isPlatformBrowser(this.platformId)) {
      // Сохраняем текущую позицию скролла основного вида
      const currentMainScrollPosition = window.scrollY;

      this.isFullscreenBookDialogActive = true;
      this.fullscreenDialog.nativeElement.showModal();

      // Sync current scroll position before opening
      this.syncScrollPositions(false);

      // Ensure we have chapter content
      if (!this.chapterContent) {
        this.getChapter(this.chapter);
      }

      // Restore or calculate fullscreen scroll position after content loads
      setTimeout(() => {
        const savedPosition = this.getSavedReadingPosition();
        if (savedPosition && savedPosition.fullscreenScrollPosition > 0) {
          // Используем сохраненную позицию полноэкранного режима
          this.setFullscreenScrollPosition(savedPosition.fullscreenScrollPosition);
        } else {
          // Конвертируем текущую позицию основного вида в полноэкранную
          this.convertMainScrollToFullscreen(currentMainScrollPosition);
        }
      }, 100);
    }
  }

  // Add this method to close the dialog
  closeFullscreenDialog() {
    // Get current fullscreen scroll position and chapter before closing
    const fullscreenScrollPosition = this.getFullscreenScrollPosition();
    const currentChapter = this.chapter;

    // Sync fullscreen scroll position back to normal view before closing
    this.syncScrollPositions(true);

    this.isFullscreenBookDialogActive = false;
    this.fullscreenDialog.nativeElement.close();

    // After closing, ensure main view shows the same chapter and scroll position
    setTimeout(() => {
      if (isPlatformBrowser(this.platformId)) {
        // If chapter content is not loaded in main view or chapter changed, reload it
        if (!this.chapterContent || this.tab !== 'Читать') {
          // Switch to reading tab if not already there
          if (this.tab !== 'Читать') {
            this.tab = 'Читать';
          }

          // Load the same chapter that was in fullscreen
          this.getChapter(currentChapter);
        }

        // Scroll to corresponding position after a short delay to ensure content is loaded
        setTimeout(() => {
          if (fullscreenScrollPosition > 0) {
            this.scrollToCorrespondingPosition(fullscreenScrollPosition);
          }
        }, 200);
      }
    }, 100);
  }











  showScrollTop: boolean = false;
  similarCall: boolean = false;
  private scrollSaveTimeout: any = null;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (isPlatformBrowser(this.platformId)) {
      this.showScrollTop = window.scrollY > 300;
      if(this.showScrollTop){

          if(this.similar.length === 0 && !this.similarCall) {
            this.similarCall = true;
            this.libraryService.getSimilar(this.bookId).subscribe((res: any) => {
              this.similar = res;
            });
          }
      }

      // Сохраняем позицию скролла с задержкой (debounce)
      if (this.tab === 'Читать' && this.chapterContent && !this.isFullscreenBookDialogActive) {
        if (this.scrollSaveTimeout) {
          clearTimeout(this.scrollSaveTimeout);
        }
        this.scrollSaveTimeout = setTimeout(() => {
          this.saveReadingPosition(this.chapter);
        }, 1000); // Сохраняем через 1 секунду после остановки скролла
      }
    }
  }

  // Обработчик скролла для полноэкранного режима
  onFullscreenScroll(event: Event) {
    if (isPlatformBrowser(this.platformId) && this.isFullscreenBookDialogActive && this.chapterContent) {
      if (this.fullscreenScrollSaveTimeout) {
        clearTimeout(this.fullscreenScrollSaveTimeout);
      }
      this.fullscreenScrollSaveTimeout = setTimeout(() => {
        const scrollPosition = this.getFullscreenScrollPosition();
        this.readingPositionService.updateScrollPosition(this.data.id, scrollPosition, true);
      }, 1000);
    }
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -260,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 260,
        behavior: 'smooth'
      });
    }
  }

  // Navigation methods for fullscreen mode
  nextFullscreen() {
    if (this.chapter < this.chapterCount - 1) {
      // Save current fullscreen scroll position before changing chapter
      this.syncScrollPositions(true);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter + 1);
      this.scrollToTopFullscreen();
    }
  }

  prevFullscreen() {
    if (this.chapter > 0) {
      // Save current fullscreen scroll position before changing chapter
      this.syncScrollPositions(true);
      this.chapterContent = ''; // Clear content to show loading
      this.getChapter(this.chapter - 1);
      this.scrollToTopFullscreen();
    }
  }

  goToChapterFullscreen(chapterIndex: number) {
    // Save current fullscreen scroll position before changing chapter
    this.syncScrollPositions(true);
    this.chapterContent = ''; // Clear content to show loading
    this.getChapter(chapterIndex);
    this.fullscreenContents = false; // Close contents after selection
    this.scrollToTopFullscreen();
  }

  scrollToTopFullscreen() {
    if (isPlatformBrowser(this.platformId)) {
      // Scroll to top of the fullscreen dialog content
      const dialogContent = this.fullscreenDialog.nativeElement.querySelector('.book-pages-container');
      if (dialogContent) {
        dialogContent.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (isPlatformBrowser(this.platformId)) {
      const target = event.target as HTMLElement;
      const settingsContainer = document.querySelector('.settings-container');

      // Если клик вне settings-container, закрываем меню
      if (settingsContainer && !settingsContainer.contains(target)) {
        this.showFullscreenOptions = false;
      }
    }
  }

  // Метод для получения стилей размера шрифта
  getFullscreenFontSizeStyles() {
    const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);
    return {
      'font-size': this.fullscreenBookFontSize + 'px',
      'line-height': lineHeight + 'px',
      '--fullscreen-font-size': this.fullscreenBookFontSize + 'px',
      '--fullscreen-line-height': lineHeight + 'px'
    };
  }

  // Метод для получения стилей ширины текста
  getFullscreenTextWidthStyles() {
    const widthMap = {
      'narrow': '700px',
      'medium': '930px',
      'full': '100%'
    };
    return {
      'max-width': widthMap[this.fullscreenBookTextWidth as keyof typeof widthMap],
      'margin': '0 auto'
    };
  }

  // Метод для применения стилей через JavaScript
  applyFullscreenFontSize() {
    if (isPlatformBrowser(this.platformId)) {
      setTimeout(() => {
        const lineHeight = this.calculateProportionalLineHeight(this.fullscreenBookFontSize);

        // Находим все элементы текста внутри text-interaction
        const textElements = document.querySelectorAll('.fullscreen-book-dialog text-interaction *');

        textElements.forEach((element: any) => {
          // Сохраняем текущий font-weight перед изменением размера
          const currentFontWeight = window.getComputedStyle(element).fontWeight;
          element.style.fontSize = this.fullscreenBookFontSize + 'px';
          element.style.lineHeight = lineHeight + 'px';
          element.style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          element.style.setProperty('line-height', lineHeight + 'px', 'important');
          // Восстанавливаем font-weight
          if (currentFontWeight && currentFontWeight !== 'normal') {
            element.style.setProperty('font-weight', currentFontWeight, 'important');
          }
        });

        // Также применяем к самому text-interaction
        const textInteraction = document.querySelector('.fullscreen-book-dialog text-interaction');
        if (textInteraction) {
          const currentFontWeight = window.getComputedStyle(textInteraction).fontWeight;
          (textInteraction as HTMLElement).style.fontSize = this.fullscreenBookFontSize + 'px';
          (textInteraction as HTMLElement).style.lineHeight = lineHeight + 'px';
          (textInteraction as HTMLElement).style.setProperty('font-size', this.fullscreenBookFontSize + 'px', 'important');
          (textInteraction as HTMLElement).style.setProperty('line-height', lineHeight + 'px', 'important');
          if (currentFontWeight && currentFontWeight !== 'normal') {
            (textInteraction as HTMLElement).style.setProperty('font-weight', currentFontWeight, 'important');
          }
        }
      }, 500);
    }
  }


}

interface ChapterData {
  chapter: string;
  url: string;
  time: number
}
